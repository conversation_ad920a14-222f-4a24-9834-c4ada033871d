# Enhanced Global Navigation with Brand Layout Implementation Plan

## 🎯 Executive Summary

Enhancement of the global navigation component (`navbar.tsx`) to display rich brand information through a custom full-screen layout when hovering over navigation items that reference the `brandsPage` document type. The solution leverages **Next.js 15 server components with data fetching pushed to the leaves**, follows proper server/client component boundaries, and extends the existing navigation pattern.

## 📋 Current State Analysis

### Existing Infrastructure
- **Navigation Component**: `src/components/global/navbar.tsx` (Client Component)
- **Navigation Logic**:
    - Handles `menuItemType === "group"` for grouped pages
    - Handles `menuItemType === "single"` for individual pages with optional children
    - Uses `resolveHref()` function to map document types to URLs
- **Page Reference Types**: Includes `brandsPage` as a valid navigation target
- **Data Sources**:
    - Brands: Available via Sanity queries
    - Models: Available via vehicle model queries
    - Offers: Available via filtered offer queries

### Current Navigation Pattern
```typescript
// Existing logic for handling different menu types
if (item.menuItemType === "group") {
  // Render dropdown with multiple page references
} else if (item.menuItemType === "single") {
  // Check for children or render simple link
}
```

## 🏗️ Architecture Design (Next.js 15 Best Practices)

### Component Hierarchy - Proper Server/Client Boundaries
```
src/
├── app/(frontend)/
│   └── layout.tsx (Server - NO brand data fetching here)
├── components/global/
│   ├── navbar.tsx (Client - Navigation Logic Only)
│   └── navigation/
│       ├── navigation-layout.tsx (Server - Orchestrates Everything)
│       ├── brand-navigation-trigger.tsx (Client - Hover Logic Only)
│       ├── brand-navigation-content.tsx (Server - Layout Shell)
│       ├── brand-grid-loader.tsx (Server - Data Fetching)
│       ├── brand-preview-card.tsx (Server - Individual Brand)
│       ├── brand-models-loader.tsx (Server - Model Data)
│       └── brand-offers-loader.tsx (Server - Offer Data)
```

### Data Flow Architecture (Proper Composition Pattern)
1. **Server Navigation Layout** orchestrates server components and renders client trigger
2. **Client Trigger** receives server components as children props
3. **Server Content Components** handle all data fetching at leaves
4. **Suspense Boundaries** handle loading states per data type
5. **Client Component** only manages hover state and visibility

### Key Architectural Decisions
- ✅ **Server components as children** - Passed to client components via props
- ✅ **Composition over imports** - No server component imports in client code
- ✅ **Data fetching at leaves** - Each server component fetches its own data
- ✅ **Minimal client logic** - Only hover state and event handlers
- ✅ **Streaming & Suspense** - Progressive rendering per data type

## 💻 Technical Implementation

### Phase 1: Layout Detection Strategy

#### Document Type Detection Pattern
Following the existing `resolveHref` pattern, add layout detection:

```typescript
// src/lib/navigation-layout.ts
export function getNavigationLayoutType(pageReference?: { _type?: string }): 'brand' | null {
  if (!pageReference?._type) return null;
  
  switch (pageReference._type) {
    case 'brandsPage':
      return 'brand';
    // Future: Add more custom layouts here
    default:
      return null;
  }
}
```

### Phase 2: Server Component - Navigation Layout Orchestrator

#### Navigation Layout Orchestrator (Server Component)
```typescript
// src/components/global/navigation/navigation-layout.tsx
import { Suspense } from 'react';
import { BrandNavigationTrigger } from './brand-navigation-trigger';
import { BrandNavigationContent } from './brand-navigation-content';
import { BrandGridLoader } from './brand-grid-loader';
import { BrandGridSkeleton } from './brand-grid-skeleton';

interface NavigationLayoutProps {
  title: string;
  layoutType: 'brand' | null;
}

export function NavigationLayout({ title, layoutType }: NavigationLayoutProps) {
  if (layoutType === 'brand') {
    // Server component orchestrates and passes children to client component
    const brandContent = (
      <BrandNavigationContent>
        <Suspense fallback={<BrandGridSkeleton />}>
          <BrandGridLoader />
        </Suspense>
      </BrandNavigationContent>
    );

    return (
      <BrandNavigationTrigger title={title}>
        {brandContent}
      </BrandNavigationTrigger>
    );
  }

  // Fallback for non-brand layouts
  return null;
}
```

### Phase 3: Client Component - Navigation Trigger (Minimal)

#### Brand Navigation Trigger (Client Component - Uses Existing NavigationMenu Pattern)
```typescript
// src/components/global/navigation/brand-navigation-trigger.tsx
"use client";

import { ReactNode } from 'react';
import { NavigationMenuItem, NavigationMenuTrigger, NavigationMenuContent } from '@/components/ui/navigation-menu';

interface BrandNavigationTriggerProps {
  title: string;
  children: ReactNode; // Server components passed as children
}

export function BrandNavigationTrigger({ title, children }: BrandNavigationTriggerProps) {
  return (
    <NavigationMenuItem>
      <NavigationMenuTrigger className="group-hover/nav:opacity-40 hover:opacity-100! bg-transparent">
        {title}
      </NavigationMenuTrigger>
      <NavigationMenuContent className="w-screen max-w-none">
        {children}
      </NavigationMenuContent>
    </NavigationMenuItem>
  );
}
```

### Phase 4: Server Components - Data Fetching at Leaves

#### Brand Navigation Content (Server Component)
```typescript
// src/components/global/navigation/brand-navigation-content.tsx
import { ReactNode } from 'react';

interface BrandNavigationContentProps {
  children: ReactNode;
}

export function BrandNavigationContent({ children }: BrandNavigationContentProps) {
  return (
    <div className="py-8">
      <div className="container mx-auto">
        <h3 className="text-xl font-semibold mb-6">Browse by Brand</h3>
        {children}
      </div>
    </div>
  );
}
```

#### Brand Grid Data Loader (Server Component)
```typescript
// src/components/global/navigation/brand-grid-loader.tsx
import { sanityFetch } from '@/sanity/lib/fetch';
import { brandNavigationGridQuery } from '@/sanity/lib/queries/fragments/navigation';
import { BrandPreviewCard } from './brand-preview-card';

export async function BrandGridLoader() {
  // Data fetching happens here at the leaf using centralized query
  const brands = await sanityFetch({ 
    query: brandNavigationGridQuery,
    revalidate: 3600 // 1 hour cache
  });

  if (!brands?.data?.length) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        No brands available
      </div>
    );
  }

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
      {brands.data.map((brand) => (
        <BrandPreviewCard 
          key={brand._id} 
          brand={brand}
        />
      ))}
    </div>
  );
}
```

#### Individual Brand Card (Server Component)
```typescript
// src/components/global/navigation/brand-preview-card.tsx
import { Suspense } from 'react';
import Link from 'next/link';
import { BrandModelsLoader } from './brand-models-loader';
import { BrandOffersLoader } from './brand-offers-loader';

interface BrandPreviewCardProps {
  brand: {
    _id: string;
    name: string;
    slug: string;
    tagline?: string;
    logo?: any;
    brandColors?: {
      primaryColor?: string;
      secondaryColor?: string;
      accentColor?: string;
    };
  };
}

export function BrandPreviewCard({ brand }: BrandPreviewCardProps) {
  return (
    <div className="group relative">
      <Link 
        href={`/brands/${brand.slug}`}
        className="block p-4 rounded-lg border hover:shadow-md transition-all"
        style={{
          '--brand-primary': brand.brandColors?.primaryColor || '#000',
          '--brand-accent': brand.brandColors?.accentColor || '#666'
        } as React.CSSProperties}
      >
        {/* Brand logo and basic info */}
        <div className="text-center">
          {brand.logo && (
            <img 
              src={brand.logo.asset.url} 
              alt={brand.name}
              className="w-12 h-12 mx-auto mb-2"
            />
          )}
          <h4 className="font-medium">{brand.name}</h4>
          {brand.tagline && (
            <p className="text-sm text-muted-foreground">{brand.tagline}</p>
          )}
        </div>
      </Link>

      {/* Hover details - loaded separately */}
      <div className="absolute top-full left-0 w-80 bg-background border border-border shadow-lg rounded-lg p-4 opacity-0 group-hover:opacity-100 transition-opacity z-50">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <h5 className="font-medium mb-2">Featured Models</h5>
            <Suspense fallback={<div className="text-sm text-muted-foreground">Loading...</div>}>
              <BrandModelsLoader brandId={brand._id} />
            </Suspense>
          </div>
          
          <div>
            <h5 className="font-medium mb-2">Current Offers</h5>
            <Suspense fallback={<div className="text-sm text-muted-foreground">Loading...</div>}>
              <BrandOffersLoader brandId={brand._id} />
            </Suspense>
          </div>
        </div>
      </div>
    </div>
  );
}
```

#### Brand Models Loader (Server Component)
```typescript
// src/components/global/navigation/brand-models-loader.tsx
import { sanityFetch } from '@/sanity/lib/fetch';
import { brandFeaturedModelsQuery } from '@/sanity/lib/queries/fragments/navigation';

interface BrandModelsLoaderProps {
  brandId: string;
}

export async function BrandModelsLoader({ brandId }: BrandModelsLoaderProps) {
  // Each component fetches its own data using centralized query
  const models = await sanityFetch({ 
    query: brandFeaturedModelsQuery,
    params: { brandId },
    revalidate: 1800 // 30 minutes cache
  });

  if (!models?.data?.length) {
    return <div className="text-sm text-muted-foreground">No featured models</div>;
  }

  return (
    <div className="space-y-2">
      {models.data.map((model) => (
        <div key={model._id} className="text-sm">
          <div className="font-medium">{model.name}</div>
          <div className="text-muted-foreground">{model.modelYear}</div>
        </div>
      ))}
    </div>
  );
}
```

#### Brand Offers Loader (Server Component)
```typescript
// src/components/global/navigation/brand-offers-loader.tsx
import { sanityFetch } from '@/sanity/lib/fetch';
import { brandFeaturedOffersQuery } from '@/sanity/lib/queries/fragments/navigation';

interface BrandOffersLoaderProps {
  brandId: string;
}

export async function BrandOffersLoader({ brandId }: BrandOffersLoaderProps) {
  // Each component fetches its own data using centralized query
  const offers = await sanityFetch({ 
    query: brandFeaturedOffersQuery,
    params: { brandId },
    revalidate: 900 // 15 minutes cache
  });

  if (!offers?.data?.length) {
    return <div className="text-sm text-muted-foreground">No current offers</div>;
  }

  return (
    <div className="space-y-2">
      {offers.data.map((offer) => (
        <div key={offer._id} className="text-sm">
          <div className="font-medium">{offer.title}</div>
          <div className="text-green-600">{offer.savings}</div>
        </div>
      ))}
    </div>
  );
}
```

### Phase 5: Integration with Existing Navbar

#### Updated Navbar Logic (Client Component)
```typescript
// In src/components/global/navbar.tsx
import { getNavigationLayoutType } from '@/lib/navigation-layout';
import { NavigationLayout } from './navigation/navigation-layout';

// Inside the navigation rendering logic:
{processedNavItems.map((item) => {
  // For group items - check if any reference is brandsPage
  if (item.menuItemType === "group") {
    const hasBrandsPage = item.pageReferences?.some(
      page => getNavigationLayoutType(page) === 'brand'
    );
    
    if (hasBrandsPage) {
      // Server component will orchestrate everything
      return (
        <NavigationLayout 
          key={item._key} 
          title={item.title}
          layoutType="brand"
        />
      );
    }
    // Continue with default group rendering
  }
  
  // For single items - check if reference is brandsPage
  if (item.menuItemType === "single") {
    const layoutType = getNavigationLayoutType(item.pageReference);
    
    if (layoutType === 'brand') {
      // Server component will orchestrate everything
      return (
        <NavigationLayout 
          key={item._key} 
          title={item.pageReference?.title || item.title}
          layoutType={layoutType}
        />
      );
    }
    // Continue with default single item rendering
  }
})}
```
