---
title: "Offers System Implementation Plan"
description: "Comprehensive plan for implementing high-quality offers system frontend leveraging full Sanity offer schema"
category: "autocorner"
subcategory: "features"
status: "active"
lastUpdated: "2025-08-04"
tags:
  - "offers"
  - "implementation"
  - "frontend"
  - "sanity"
  - "groq"
---

# Offers System Implementation Plan

## 🎯 **Current State Analysis**

### **Issues Identified**
1. **Query Limitations**: Current GROQ queries only fetch basic fields (title, slug, excerpt, image)
2. **Schema Underutilization**: Rich offer schema with 17 field groups not properly leveraged
3. **Component Gaps**: Basic components don't showcase financial details, benefits, validity periods
4. **Missing Features**: No filtering, eligibility checking, financial calculator, brand integration

### **Available Data Examples**
From existing offers, we have rich data including:
- **Brand Integration**: Dynamic brand colors (Audi: #BB0A30, Škoda: #007B3A) with logos
- **Financial Details**: 0% leasing rates, monthly payments, currency (CHF), complete financing structures
- **Key Benefits**: Multiple benefits with descriptions (up to 5 per offer, currently no icons)
- **Rich Text Content**: Portable text descriptions with detailed vehicle information
- **Validity Periods**: Both limited time (with dates) and unlimited offers
- **Display Settings**: Featured flags, priority ordering, homepage display controls
- **Savings Information**: Fixed discounts (CHF 1,000), percentage discounts, rebate structures
- **Eligibility Criteria**: Required criteria with descriptions (residence, client type)
- **Vehicle Models**: Direct references to applicable models with brand links
- **Contact Information**: Person details, roles, phone, email, application URLs
- **Media Assets**: High-quality images with blur hash, metadata, captions (no videos yet)
- **SEO Integration**: Complete SEO objects with titles, descriptions, images

### **Layout Pattern Analysis**
Based on blog posts and brand hub architecture, we'll use the **12-column layout with sticky sidebars**:

#### **Offer Detail Page Layout (Following Brand Hub Pattern)**
```tsx
<div className="grid grid-cols-12 gap-y-10 xl:gap-20 max-w-10xl mx-auto"
  style={{
    "--brand-color-primary": getBrandColor(offer.brand?.brandColors?.primaryColor),
    "--brand-color-secondary": getBrandColor(offer.brand?.brandColors?.secondaryColor),
    "--brand-color-accent": getBrandColor(offer.brand?.brandColors?.accentColor),
  }}>
  
  {/* Left Sidebar - Navigation & Back Button */}
  <aside className="col-span-12 xl:col-span-2 xl:sticky xl:top-28 h-fit">
    <BackButton href="/offers" title="Back to Offers" />
    <OfferNavigation offer={offer} />
  </aside>
  
  {/* Main Content - Rich Text & Details */}
  <main className="col-span-12 xl:col-span-7 xl:px-10 xl:border-x xl:border-dashed">
    <OfferHero offer={offer} />
    <OfferTabs offer={offer} />
    <OfferRichText content={offer.description} />
  </main>
  
  {/* Right Sidebar - Actions & Widgets */}
  <aside className="col-span-12 xl:col-span-3 xl:sticky xl:top-28 h-fit space-y-5">
    <OfferQuickActions offer={offer} />
    <OfferSidebarContent offer={offer} />
  </aside>
</div>
```

## 🏗️ **Architecture Plan**

### **1. Enhanced GROQ Queries**

#### **Comprehensive Offers Listing Query**
```groq
*[_type == "offer" && displaySettings.isActive == true] 
| order(displaySettings.priority asc, displaySettings.isFeatured desc, _createdAt desc) {
  _id,
  _type,
  title,
  "slug": slug.current,
  offerType,
  shortDescription,
  
  // Brand integration with colors
  brand->{
    _id,
    name,
    "slug": slug.current,
    brandColors,
    logo
  },
  
  // Optimized visual assets
  mainImage{
    asset->{
      _id,
      url,
      metadata{dimensions, blurHash}
    },
    altText,
    caption,
    hotspot,
    crop
  },
  
  // Key benefits preview
  keyBenefits[0...3]{
    title,
    description,
    icon{asset->{url}, altText}
  },
  
  // Validity and urgency
  validityPeriod{
    isLimitedTime,
    validFrom,
    validTo
  },
  
  // Financial highlights
  financingDetails{
    financingType,
    interestRate,
    monthlyPayment,
    currency
  },
  
  // Savings information
  savings{
    discountType,
    discountValue,
    discountPercentage,
    rebateAmount
  },
  
  // Display settings
  displaySettings{
    isFeatured,
    showOnHomepage,
    priority
  },
  
  // Redemption tracking
  maxRedemptions,
  currentRedemptions,
  
  // SEO
  seo{
    title,
    description,
    noIndex,
    image
  }
}
```

#### **Individual Offer Detail Query**
```groq
*[_type == "offer" && slug.current == $slug][0] {
  _id,
  _type,
  title,
  "slug": slug.current,
  offerCode,
  offerType,
  shortDescription,
  description,
  
  // Complete brand information
  brand->{
    _id,
    name,
    "slug": slug.current,
    brandColors,
    logo,
    description,
    contactInformation
  },
  
  // All visual assets
  mainImage{
    asset->{
      _id,
      url,
      metadata{dimensions, blurHash, palette}
    },
    altText,
    caption,
    hotspot,
    crop
  },
  
  gallery[]{
    asset->{_id, url, metadata{dimensions, blurHash}},
    altText,
    caption,
    hotspot,
    crop
  },
  
  offerVideo{
    asset->{assetId, playbackId, status}
  },
  
  // Complete benefits
  keyBenefits[]{
    title,
    description,
    icon{asset->{url}, altText}
  },
  
  // Validity details
  validityPeriod{
    validFrom,
    validTo,
    isLimitedTime
  },
  maxRedemptions,
  currentRedemptions,
  
  // Eligibility
  targetAudience[],
  eligibilityCriteria[]{
    title,
    description,
    isRequired
  },
  
  // Vehicle application
  applicableModels[]->{
    _id,
    name,
    "slug": slug.current,
    brand->{name, "slug": slug.current}
  },
  
  // Complete financial details
  financingDetails{
    financingType,
    interestRate,
    duration,
    downPayment,
    monthlyPayment,
    finalPayment,
    totalAmount,
    currency
  },
  
  // Savings and discounts
  savings{
    discountType,
    discountValue,
    discountPercentage,
    maximumDiscount,
    rebateAmount,
    tradeInBonus,
    loyaltyBonus,
    referralBonus
  },
  
  // Promotional pricing
  promotionalPricing{
    originalPrice,
    promotionalPrice,
    pricingNotes
  },
  
  // Contact and application
  contactInformation{
    contactPerson,
    contactRole,
    contactPhone,
    contactEmail,
    contactCenter->{
      _id,
      name,
      address,
      phone,
      email,
      openingHours
    }
  },
  
  onlineApplication{
    isEnabled,
    applicationUrl,
    applicationFormId
  },
  
  // Legal information
  legalDisclaimer,
  termsAndConditions,
  
  // Related offers
  relatedOffers[]->{
    _id,
    title,
    "slug": slug.current,
    offerType,
    shortDescription,
    mainImage{asset->{url}, altText},
    brand->{name, "slug": slug.current}
  },
  
  // Display and SEO
  displaySettings,
  seo
}
```

#### **Filtered Offers Query**
```groq
*[_type == "offer" 
  && displaySettings.isActive == true 
  && ($offerType == null || offerType == $offerType)
  && ($brandSlug == null || brand->slug.current == $brandSlug)
  && ($isLimitedTime == null || validityPeriod.isLimitedTime == $isLimitedTime)
  && ($isFeatured == null || displaySettings.isFeatured == $isFeatured)
  && ($hasFinancing == null || financingDetails != null)
] | order(
  displaySettings.priority asc, 
  displaySettings.isFeatured desc, 
  _createdAt desc
) [$offset...$limit] {
  _id,
  title,
  "slug": slug.current,
  offerType,
  shortDescription,
  brand->{name, "slug": slug.current, brandColors},
  mainImage{asset->{url}, altText},
  validityPeriod{isLimitedTime, validFrom, validTo},
  financingDetails{financingType, interestRate, currency},
  savings{discountType, discountValue, discountPercentage},
  displaySettings{isFeatured, priority}
}
```

### **2. Component Architecture**

#### **Core Components Structure**
```
src/
├── app/(frontend)/offers/
│   ├── page.tsx (Enhanced listing with filtering)
│   ├── [slug]/page.tsx (Comprehensive detail page with sidebar layout)
│   └── _components/
│       ├── offers-grid.tsx
│       ├── offers-filters.tsx
│       ├── offer-content.tsx (Main content with rich text)
│       ├── offer-hero.tsx
│       ├── offer-tabs.tsx
│       ├── offer-navigation.tsx (Side navigation)
│       ├── offer-sidebar-content.tsx (Sidebar widgets)
│       ├── financial-calculator.tsx
│       ├── eligibility-checker.tsx
│       ├── validity-display.tsx
│       ├── contact-form.tsx
│       └── benefits-showcase.tsx
├── components/offers/ (Shared/Reusable components)
│   ├── offer-card.tsx (Reusable across pages)
│   ├── offer-widget.tsx (Compact widget for sidebars)
│   ├── offers-carousel.tsx (Homepage/related offers)
│   ├── offer-type-badge.tsx
│   ├── offer-validity-badge.tsx
│   ├── offer-savings-display.tsx
│   ├── offer-redemption-status.tsx
│   ├── offer-brand-integration.tsx (Dynamic brand colors)
│   └── offer-rich-text.tsx (Portable text with custom components)
└── sanity/lib/queries/documents/
    └── offer.ts (Enhanced GROQ queries)
```

### **3. Brand Color Integration Pattern**
```typescript
// Use the existing getBrandColor utility
import { getBrandColor } from "@/components/brands/models/utils";

// Apply brand colors using CSS custom properties (following brand hub pattern)
const brandColorStyles = {
  "--brand-color-primary": getBrandColor(offer.brand?.brandColors?.primaryColor),
  "--brand-color-secondary": getBrandColor(offer.brand?.brandColors?.secondaryColor),
  "--brand-color-accent": getBrandColor(offer.brand?.brandColors?.accentColor),
};

// Usage in components (following button.tsx pattern)
<div style={brandColorStyles}>
  {/* Button with brand override */}
  <Button 
    className="brand" 
    variant="primary"
  >
    Contact Us
  </Button>
  
  {/* Custom elements with brand colors */}
  <div className="border-l-4 border-[var(--brand-color-primary)] pl-4">
    <AnimatedUnderline className="bg-[var(--brand-color-primary)]" />
  </div>
</div>
```

### **4. Enhanced Portable Text Integration**
```typescript
// Extend existing portable text components for offers
import { portableTextComponents } from "@/components/portable-text/portable-text-components";
import type { PortableTextComponents } from "@portabletext/react";

export const offerPortableTextComponents: PortableTextComponents = {
  ...portableTextComponents,
  
  // Override block components to include brand styling
  block: {
    ...portableTextComponents.block,
    normal: (props) => (
      <p className="mb-6 text-lg leading-relaxed text-gray-700">
        {props.children}
      </p>
    ),
    // Extend h2 with brand color accent
    h2: (props) => {
      const text = props.value.children?.[0]?.text ?? "";
      const id = slugify(text);
      return (
        <Heading
          id={id}
          size="xl"
          tag="h2"
          className="border-l-4 border-[var(--brand-color-primary)] pl-4 mb-4 mt-8"
        >
          {text}
        </Heading>
      );
    },
  },
  
  // Override marks for brand-aware links
  marks: {
    link: ({ children, value }) => (
      <a 
        href={value.href}
        className="text-[var(--brand-color-primary)] underline hover:no-underline transition-colors"
      >
        {children}
      </a>
    ),
  },
  
  // Add offer-specific types if needed
  types: {
    ...portableTextComponents.types,
    // Future: offer-specific embedded components
  },
};

// Usage in offer components
<PortableText 
  value={offer.description?.richTextContent} 
  components={offerPortableTextComponents}
/>
```

## 🎨 **Component Implementation (Using Shadcn Components)**

### **Enhanced OfferCard Component**
```typescript
import { getBrandColor } from "@/components/brands/models/utils";
import { Card, CardContent, CardFooter, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";

interface OfferCardProps {
  offer: OfferWithBrand;
  priority?: 'high' | 'normal';
  variant?: 'grid' | 'list' | 'featured';
}

export const OfferCard: React.FC<OfferCardProps> = ({ 
  offer, 
  priority = 'normal',
  variant = 'grid' 
}) => {
  const brandColorStyles = {
    "--brand-color-primary": getBrandColor(offer.brand?.brandColors?.primaryColor),
    "--brand-color-secondary": getBrandColor(offer.brand?.brandColors?.secondaryColor),
    "--brand-color-accent": getBrandColor(offer.brand?.brandColors?.accentColor),
  };
  
  const isExpiringSoon = useMemo(() => 
    offer.validityPeriod?.validTo && 
    isWithinDays(offer.validityPeriod.validTo, 7), [offer.validityPeriod]
  );
  
  const redemptionPercentage = offer.maxRedemptions 
    ? ((offer.currentRedemptions || 0) / offer.maxRedemptions) * 100 
    : 0;
  
  return (
    <Card 
      className={cn(
        "group relative overflow-hidden transition-all hover:shadow-md",
        variant === 'featured' && "md:col-span-2",
        offer.displaySettings?.isFeatured && "ring-2 ring-offset-2 ring-[var(--brand-color-primary)]",
        isExpiringSoon && "ring-orange-200"
      )} 
      style={brandColorStyles}
    >
      {/* Badges */}
      <div className="absolute top-4 left-4 z-10 flex gap-2">
        <Badge className="brand" variant="default">
          {offer.offerType}
        </Badge>
        {offer.displaySettings?.isFeatured && (
          <Badge variant="secondary">
            <Star className="mr-1 h-3 w-3" />
            Featured
          </Badge>
        )}
      </div>
      
      {/* Validity Badge */}
      {offer.validityPeriod?.isLimitedTime && (
        <div className="absolute top-4 right-4 z-10">
          <Badge variant="destructive">
            <Clock className="mr-1 h-3 w-3" />
            Limited Time
          </Badge>
        </div>
      )}
      
      {/* Main Image */}
      <div className="aspect-[16/9] overflow-hidden">
        {offer.mainImage?.asset?.url ? (
          <Image
            src={offer.mainImage.asset.url}
            alt={offer.mainImage.altText || offer.title}
            width={400}
            height={225}
            className="h-full w-full object-cover transition-transform group-hover:scale-105"
            priority={priority === 'high'}
            blurDataURL={offer.mainImage.asset.metadata?.blurHash}
            placeholder="blur"
          />
        ) : (
          <div className="flex h-full items-center justify-center bg-muted">
            <Gift className="h-16 w-16 text-muted-foreground" />
          </div>
        )}
      </div>
      
      <CardHeader className="pb-2">
        {/* Brand Link */}
        {offer.brand && (
          <Link 
            href={`/gammes/${offer.brand.slug}`}
            className="inline-flex items-center text-sm font-medium text-[var(--brand-color-primary,#666)] hover:text-[var(--brand-color-primary,#333)]"
          >
            {offer.brand.name}
            <ArrowRight className="ml-1 h-3 w-3" />
          </Link>
        )}
        
        {/* Title */}
        <h3 className="text-xl font-semibold text-card-foreground group-hover:text-muted-foreground">
          <Link href={`/offers/${offer.slug}`} className="stretched-link">
            {offer.title}
          </Link>
        </h3>
        
        {/* Short Description */}
        {offer.shortDescription && (
          <p className="text-sm text-muted-foreground line-clamp-2">
            {offer.shortDescription}
          </p>
        )}
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Key Benefits Preview */}
        {offer.keyBenefits && offer.keyBenefits.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {offer.keyBenefits.slice(0, 3).map((benefit, index) => (
              <Badge 
                key={index}
                className="brand"
                variant="secondary"
              >
                {benefit.title}
              </Badge>
            ))}
          </div>
        )}
        
        {/* Financial Highlights */}
        <div className="flex items-center justify-between text-sm">
          {offer.financingDetails && (
            <div>
              <span className="font-medium text-card-foreground">
                {offer.financingDetails.interestRate === 0 ? '0%' : `${offer.financingDetails.interestRate}%`}
              </span>
              <span className="ml-1 text-muted-foreground">
                {offer.financingDetails.financingType}
              </span>
            </div>
          )}
          
          {offer.savings && (offer.savings.discountValue > 0 || offer.savings.discountPercentage > 0) && (
            <Badge className="brand" variant="outline">
              {offer.savings.discountType === 'percentage' 
                ? `${offer.savings.discountPercentage}% OFF`
                : `CHF ${offer.savings.discountValue} OFF`
              }
            </Badge>
          )}
        </div>
        
        {/* Redemption Progress */}
        {offer.maxRedemptions && (
          <div className="space-y-2">
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>Claimed: {offer.currentRedemptions || 0}/{offer.maxRedemptions}</span>
              <span>{redemptionPercentage.toFixed(0)}% claimed</span>
            </div>
            <Progress 
              value={redemptionPercentage} 
              className="h-2 [&>div]:bg-[var(--brand-color-primary)]" 
            />
          </div>
        )}
      </CardContent>
      
      <CardFooter>
        <Button 
          className="brand w-full" 
          variant="primary"
          size="sm"
          buttonType="internal"
          pageReference={{ _type: "offer", slug: offer.slug }}
        >
          View Offer
        </Button>
      </CardFooter>
    </Card>
  );
};
```

### **Advanced Filters Component**
```typescript
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";

interface OffersFiltersProps {
  brands: BrandOption[];
  initialFilters?: OfferFilters;
  onFiltersChange: (filters: OfferFilters) => void;
}

export const OffersFilters: React.FC<OffersFiltersProps> = ({
  brands,
  initialFilters,
  onFiltersChange
}) => {
  const [filters, setFilters] = useState<OfferFilters>(initialFilters || {});
  
  const offerTypes = useMemo(() => [
    { value: 'financing', label: 'Financing' },
    { value: 'leasing', label: 'Leasing' },
    { value: 'discount', label: 'Discount' },
    { value: 'rebate', label: 'Rebate' },
    { value: 'trade_in', label: 'Trade-in' },
    { value: 'promotional', label: 'Promotional' },
    { value: 'seasonal', label: 'Seasonal' },
    { value: 'clearance', label: 'Clearance' }
  ], []);
  
  const handleFilterChange = useCallback((key: keyof OfferFilters, value: any) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onFiltersChange(newFilters);
  }, [filters, onFiltersChange]);
  
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Filters</CardTitle>
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => {
              setFilters({});
              onFiltersChange({});
            }}
          >
            Clear All
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Offer Type Filter */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">Offer Type</Label>
          <Select 
            value={filters.offerType || ''} 
            onValueChange={(value) => handleFilterChange('offerType', value || null)}
          >
            <SelectTrigger>
              <SelectValue placeholder="All Types" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Types</SelectItem>
              {offerTypes.map(type => (
                <SelectItem key={type.value} value={type.value}>
                  {type.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        <Separator />
        
        {/* Brand Filter */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">Brand</Label>
          <Select 
            value={filters.brandSlug || ''} 
            onValueChange={(value) => handleFilterChange('brandSlug', value || null)}
          >
            <SelectTrigger>
              <SelectValue placeholder="All Brands" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Brands</SelectItem>
              {brands.map(brand => (
                <SelectItem key={brand.slug} value={brand.slug}>
                  {brand.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        <Separator />
        
        {/* Feature Filters */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Features</Label>
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Checkbox 
                id="limited-time"
                checked={filters.isLimitedTime === true}
                onCheckedChange={(checked) => 
                  handleFilterChange('isLimitedTime', checked ? true : null)
                }
              />
              <Label htmlFor="limited-time" className="text-sm font-normal">
                Limited Time Only
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox 
                id="featured"
                checked={filters.isFeatured === true}
                onCheckedChange={(checked) => 
                  handleFilterChange('isFeatured', checked ? true : null)
                }
              />
              <Label htmlFor="featured" className="text-sm font-normal">
                Featured Offers
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox 
                id="has-financing"
                checked={filters.hasFinancing === true}
                onCheckedChange={(checked) => 
                  handleFilterChange('hasFinancing', checked ? true : null)
                }
              />
              <Label htmlFor="has-financing" className="text-sm font-normal">
                Financing Available
              </Label>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
```

### **Financial Calculator Component**
```typescript
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";

interface FinancialCalculatorProps {
  offer: OfferWithAllData;
}

export const FinancialCalculator: React.FC<FinancialCalculatorProps> = ({ offer }) => {
  const [vehiclePrice, setVehiclePrice] = useState(50000);
  const [downPayment, setDownPayment] = useState(offer.financingDetails?.downPayment || 20);
  const [duration, setDuration] = useState(offer.financingDetails?.duration || 48);
  const [interestRate, setInterestRate] = useState(offer.financingDetails?.interestRate || 2.9);
  
  const calculations = useMemo(() => {
    const principal = vehiclePrice * (1 - downPayment / 100);
    const monthlyRate = interestRate / 100 / 12;
    const numPayments = duration;
    
    let monthlyPayment = 0;
    if (interestRate > 0) {
      monthlyPayment = principal * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) / 
                      (Math.pow(1 + monthlyRate, numPayments) - 1);
    } else {
      monthlyPayment = principal / numPayments;
    }
    
    const totalAmount = monthlyPayment * numPayments + (vehiclePrice * downPayment / 100);
    const totalInterest = totalAmount - vehiclePrice;
    
    return {
      principal,
      monthlyPayment,
      totalAmount,
      totalInterest,
      downPaymentAmount: vehiclePrice * downPayment / 100
    };
  }, [vehiclePrice, downPayment, duration, interestRate]);
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Financial Calculator</CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Input Controls */}
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <div className="space-y-2">
            <Label htmlFor="vehicle-price">Vehicle Price (CHF)</Label>
            <Input
              id="vehicle-price"
              type="number"
              value={vehiclePrice}
              onChange={(e) => setVehiclePrice(Number(e.target.value))}
              min={10000}
              max={200000}
              step={1000}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="down-payment">Down Payment (%)</Label>
            <div className="space-y-3">
              <Slider
                value={[downPayment]}
                onValueChange={(value) => setDownPayment(value[0])}
                max={50}
                min={0}
                step={5}
                className="[&>span:first-child]:h-2 [&>span:first-child]:bg-muted [&>span>span]:bg-[var(--brand-color-primary)] [&>span>span]:border-[var(--brand-color-primary)]"
              />
              <div className="text-center text-sm text-muted-foreground">
                {downPayment}% (CHF {calculations.downPaymentAmount.toLocaleString()})
              </div>
            </div>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="duration">Duration (months)</Label>
            <Select value={duration.toString()} onValueChange={(value) => setDuration(Number(value))}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="12">12 months</SelectItem>
                <SelectItem value="36">36 months</SelectItem>
                <SelectItem value="48">48 months</SelectItem>
                <SelectItem value="60">60 months</SelectItem>
                <SelectItem value="72">72 months</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="interest-rate">Interest Rate (%)</Label>
            <Input
              id="interest-rate"
              type="number"
              value={interestRate}
              onChange={(e) => setInterestRate(Number(e.target.value))}
              min={0}
              max={15}
              step={0.1}
              disabled={offer.financingDetails?.interestRate === 0}
            />
            {offer.financingDetails?.interestRate === 0 && (
              <p className="text-sm text-green-600">Special 0% offer rate applied</p>
            )}
          </div>
        </div>
        
        <Separator />
        
        {/* Results */}
        <Card className="bg-muted/50">
          <CardHeader>
            <CardTitle className="text-lg">Calculation Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
              <div className="text-center space-y-1">
                <p className="text-sm text-muted-foreground">Monthly Payment</p>
                <p className="text-2xl font-bold text-[var(--brand-color-primary)]">
                  CHF {Math.round(calculations.monthlyPayment).toLocaleString()}
                </p>
              </div>
              <div className="text-center space-y-1">
                <p className="text-sm text-muted-foreground">Down Payment</p>
                <p className="text-xl font-semibold">
                  CHF {Math.round(calculations.downPaymentAmount).toLocaleString()}
                </p>
              </div>
              <div className="text-center space-y-1">
                <p className="text-sm text-muted-foreground">Total Amount</p>
                <p className="text-xl font-semibold">
                  CHF {Math.round(calculations.totalAmount).toLocaleString()}
                </p>
              </div>
              <div className="text-center space-y-1">
                <p className="text-sm text-muted-foreground">Total Interest</p>
                <p className="text-xl font-semibold">
                  CHF {Math.round(calculations.totalInterest).toLocaleString()}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* Disclaimer */}
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            This calculator provides estimates only. Final terms and conditions may vary. 
            Please contact us for official quotes and detailed terms.
          </AlertDescription>
        </Alert>
      </CardContent>
    </Card>
  );
};
```

### **Offer Detail Tabs Component**
```typescript
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Separator } from "@/components/ui/separator";

interface OfferDetailTabsProps {
  offer: OfferWithAllData;
}

export const OfferDetailTabs: React.FC<OfferDetailTabsProps> = ({ offer }) => {
  return (
    <Tabs defaultValue="overview" className="w-full">
      <TabsList className="grid w-full grid-cols-5">
        <TabsTrigger value="overview">Overview</TabsTrigger>
        <TabsTrigger value="benefits">Benefits</TabsTrigger>
        <TabsTrigger value="eligibility">Eligibility</TabsTrigger>
        <TabsTrigger value="calculator">Calculator</TabsTrigger>
        <TabsTrigger value="contact">Contact</TabsTrigger>
      </TabsList>
      
      <TabsContent value="overview" className="mt-6">
        <Card>
          <CardHeader>
            <CardTitle>Offer Overview</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Rich Text Description */}
            {offer.description && (
              <div className="prose prose-lg max-w-none">
                <PortableText 
                  value={offer.description.richTextContent} 
                  components={offerPortableTextComponents}
                />
              </div>
            )}
            
            <Separator />
            
            {/* Vehicle Models */}
            {offer.applicableModels && offer.applicableModels.length > 0 && (
              <div>
                <h4 className="font-semibold mb-3">Applicable Models</h4>
                <div className="flex flex-wrap gap-2">
                  {offer.applicableModels.map((model) => (
                    <Badge key={model._id} className="brand" variant="secondary">
                      {model.brand?.name} {model.name}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </TabsContent>
      
      <TabsContent value="benefits" className="mt-6">
        <Card>
          <CardHeader>
            <CardTitle>Key Benefits</CardTitle>
          </CardHeader>
          <CardContent>
            {offer.keyBenefits && offer.keyBenefits.length > 0 ? (
              <div className="grid gap-4">
                {offer.keyBenefits.map((benefit, index) => (
                  <Card key={index} className="border-l-4 border-l-[var(--brand-color-primary)]">
                    <CardHeader className="pb-2">
                      <div className="flex items-center gap-3">
                        {benefit.icon?.asset?.url && (
                          <Image
                            src={benefit.icon.asset.url}
                            alt={benefit.icon.altText || benefit.title}
                            width={24}
                            height={24}
                            className="flex-shrink-0"
                          />
                        )}
                        <CardTitle className="text-lg">{benefit.title}</CardTitle>
                      </div>
                    </CardHeader>
                    {benefit.description && (
                      <CardContent>
                        <p className="text-sm text-muted-foreground">{benefit.description}</p>
                      </CardContent>
                    )}
                  </Card>
                ))}
              </div>
            ) : (
              <p className="text-muted-foreground">No specific benefits listed for this offer.</p>
            )}
          </CardContent>
        </Card>
      </TabsContent>
      
      <TabsContent value="eligibility" className="mt-6">
        <Card>
          <CardHeader>
            <CardTitle>Eligibility Requirements</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Target Audience */}
            {offer.targetAudience && offer.targetAudience.length > 0 && (
              <div>
                <h4 className="font-semibold mb-3">Target Audience</h4>
                <div className="flex flex-wrap gap-2">
                  {offer.targetAudience.map((audience, index) => (
                    <Badge key={index} className="brand" variant="outline">
                      {audience.replace('_', ' ').toUpperCase()}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
            
            <Separator />
            
            {/* Eligibility Criteria */}
            {offer.eligibilityCriteria && offer.eligibilityCriteria.length > 0 && (
              <div>
                <h4 className="font-semibold mb-3">Requirements</h4>
                <Accordion type="single" collapsible className="w-full">
                  {offer.eligibilityCriteria.map((criteria, index) => (
                    <AccordionItem key={index} value={`item-${index}`}>
                      <AccordionTrigger className="text-left">
                        <div className="flex items-center gap-2">
                          {criteria.isRequired && (
                            <Badge variant="destructive" className="text-xs">Required</Badge>
                          )}
                          <span>{criteria.title}</span>
                        </div>
                      </AccordionTrigger>
                      <AccordionContent>
                        <p className="text-sm text-muted-foreground">{criteria.description}</p>
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </div>
            )}
          </CardContent>
        </Card>
      </TabsContent>
      
      <TabsContent value="calculator" className="mt-6">
        <FinancialCalculator offer={offer} />
      </TabsContent>
      
      <TabsContent value="contact" className="mt-6">
        <Card>
          <CardHeader>
            <CardTitle>Contact Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {offer.contactInformation && (
              <div className="space-y-4">
                {/* Contact Person */}
                <div>
                  <h4 className="font-semibold">{offer.contactInformation.contactPerson}</h4>
                  {offer.contactInformation.contactRole && (
                    <p className="text-sm text-muted-foreground">{offer.contactInformation.contactRole}</p>
                  )}
                </div>
                
                <Separator />
                
                {/* Contact Details */}
                <div className="grid gap-3">
                  {offer.contactInformation.contactPhone && (
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      <a 
                        href={`tel:${offer.contactInformation.contactPhone}`}
                        className="text-[var(--brand-color-primary)] hover:underline"
                      >
                        {offer.contactInformation.contactPhone}
                      </a>
                    </div>
                  )}
                  
                  {offer.contactInformation.contactEmail && (
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <a 
                        href={`mailto:${offer.contactInformation.contactEmail}`}
                        className="text-[var(--brand-color-primary)] hover:underline"
                      >
                        {offer.contactInformation.contactEmail}
                      </a>
                    </div>
                  )}
                </div>
                
                {/* Center Information */}
                {offer.contactInformation.contactCenter && (
                  <>
                    <Separator />
                    <div>
                      <h4 className="font-semibold mb-2">{offer.contactInformation.contactCenter.name}</h4>
                      {offer.contactInformation.contactCenter.address && (
                        <p className="text-sm text-muted-foreground mb-2">
                          {offer.contactInformation.contactCenter.address}
                        </p>
                      )}
                      {offer.contactInformation.contactCenter.openingHours && (
                        <p className="text-sm text-muted-foreground">
                          {offer.contactInformation.contactCenter.openingHours}
                        </p>
                      )}
                    </div>
                  </>
                )}
              </div>
            )}
            
            {/* Action Buttons */}
            <div className="flex flex-col gap-3 sm:flex-row pt-4">
              <Button 
                className="brand flex-1" 
                variant="primary"
                size="lg"
              >
                <Phone className="mr-2 h-4 w-4" />
                Call Now
              </Button>
              
              {offer.onlineApplication?.isEnabled && offer.onlineApplication.applicationUrl && (
                <Button 
                  variant="outline-solid" 
                  size="lg"
                  className="flex-1"
                  buttonType="external"
                  externalUrl={offer.onlineApplication.applicationUrl}
                >
                  <ExternalLink className="mr-2 h-4 w-4" />
                  Apply Online
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  );
};
```

### **Reusable Widget Components**
```typescript
import { getBrandColor } from "@/components/brands/models/utils";
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";

interface OfferWidgetProps {
  offer: OfferPreview;
  variant?: 'compact' | 'card' | 'horizontal';
  showBrandColors?: boolean;
  className?: string;
}

export const OfferWidget: React.FC<OfferWidgetProps> = ({ 
  offer, 
  variant = 'compact',
  showBrandColors = true,
  className 
}) => {
  const brandColorStyles = showBrandColors ? {
    "--brand-color-primary": getBrandColor(offer.brand?.brandColors?.primaryColor),
    "--brand-color-secondary": getBrandColor(offer.brand?.brandColors?.secondaryColor),
    "--brand-color-accent": getBrandColor(offer.brand?.brandColors?.accentColor),
  } : {};
  
  if (variant === 'horizontal') {
    return (
      <Card 
        className={cn(
          "transition-all hover:shadow-md",
          offer.displaySettings?.isFeatured && "ring-2 ring-offset-2 ring-[var(--brand-color-primary)]",
          className
        )}
        style={brandColorStyles}
      >
        <CardContent className="flex items-center p-4 space-x-4">
          {/* Image */}
          <div className="flex-shrink-0 w-16 h-16 overflow-hidden rounded">
            {offer.mainImage?.asset?.url ? (
              <Image
                src={offer.mainImage.asset.url}
                alt={offer.mainImage.altText || offer.title}
                width={64}
                height={64}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full bg-muted flex items-center justify-center">
                <Gift className="h-6 w-6 text-muted-foreground" />
              </div>
            )}
          </div>
          
          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <Badge className="brand" variant="secondary" size="sm">
                {offer.offerType}
              </Badge>
              {offer.displaySettings?.isFeatured && (
                <Badge variant="default" size="sm">Featured</Badge>
              )}
            </div>
            <CardTitle className="text-sm truncate">{offer.title}</CardTitle>
            {offer.brand && (
              <p className="text-xs text-muted-foreground">{offer.brand.name}</p>
            )}
          </div>
          
          {/* Action */}
          <Button 
            className="brand flex-shrink-0" 
            variant="outline-solid"
            size="sm"
            buttonType="internal"
            pageReference={{ _type: "offer", slug: offer.slug }}
          >
            View
          </Button>
        </CardContent>
      </Card>
    );
  }
  
  return (
    <Card 
      className={cn(
        "transition-all hover:shadow-md",
        variant === 'compact' && "h-fit",
        offer.displaySettings?.isFeatured && "ring-2 ring-offset-2 ring-[var(--brand-color-primary)]",
        className
      )}
      style={brandColorStyles}
    >
      {variant === 'card' && (
        <div className="aspect-[16/9] overflow-hidden rounded-t-lg">
          {offer.mainImage?.asset?.url ? (
            <Image
              src={offer.mainImage.asset.url}
              alt={offer.mainImage.altText || offer.title}
              width={300}
              height={169}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full bg-muted flex items-center justify-center">
              <Gift className="h-12 w-12 text-muted-foreground" />
            </div>
          )}
        </div>
      )}
      
      <CardHeader className={variant === 'compact' ? 'p-4 pb-2' : 'p-4'}>
        <div className="flex items-center gap-2 mb-2">
          <Badge className="brand" variant="secondary">
            {offer.offerType}
          </Badge>
          {offer.displaySettings?.isFeatured && (
            <Badge variant="default">Featured</Badge>
          )}
        </div>
        
        <CardTitle className="text-base leading-tight">{offer.title}</CardTitle>
        
        {offer.brand && (
          <p className="text-xs text-muted-foreground">{offer.brand.name}</p>
        )}
      </CardHeader>
      
      {variant === 'card' && offer.shortDescription && (
        <CardContent className="pt-0">
          <p className="text-sm text-muted-foreground line-clamp-2">
            {offer.shortDescription}
          </p>
        </CardContent>
      )}
      
      <CardFooter className={variant === 'compact' ? 'p-4 pt-0' : 'p-4'}>
        <Button 
          className="brand w-full" 
          variant="outline-solid"
          size="sm"
          buttonType="internal"
          pageReference={{ _type: "offer", slug: offer.slug }}
        >
          View Details
        </Button>
      </CardFooter>
    </Card>
  );
};
```

### **Offers Carousel Component**
```typescript
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface OffersCarouselProps {
  offers: OfferPreview[];
  title?: string;
  showFilters?: boolean;
  brandSlug?: string;
}

export const OffersCarousel: React.FC<OffersCarouselProps> = ({
  offers,
  title = "Current Offers",
  showFilters = false,
  brandSlug
}) => {
  const [filteredOffers, setFilteredOffers] = useState(offers);
  const [filterType, setFilterType] = useState<string>('');
  
  const displayOffers = brandSlug 
    ? filteredOffers.filter(offer => offer.brand?.slug === brandSlug)
    : filteredOffers;
  
  const handleFilterChange = (type: string) => {
    setFilterType(type);
    setFilteredOffers(
      type ? offers.filter(o => o.offerType === type) : offers
    );
  };
  
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>{title}</CardTitle>
          {showFilters && (
            <Select value={filterType} onValueChange={handleFilterChange}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="All Types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Types</SelectItem>
                <SelectItem value="financing">Financing</SelectItem>
                <SelectItem value="leasing">Leasing</SelectItem>
                <SelectItem value="discount">Discount</SelectItem>
                <SelectItem value="promotional">Promotional</SelectItem>
              </SelectContent>
            </Select>
          )}
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="flex space-x-4 overflow-x-auto pb-4">
          {displayOffers.map((offer) => (
            <div key={offer._id} className="flex-shrink-0 w-80">
              <OfferWidget offer={offer} variant="card" />
            </div>
          ))}
        </div>
        
        {displayOffers.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            No offers available for the selected filter.
          </div>
        )}
      </CardContent>
    </Card>
  );
};
```

## 📋 **Implementation Phases with Completion Tracking**

### **Phase 1: Core Infrastructure (Week 1)**
- [ ] **1.1** Update GROQ queries in `src/sanity/lib/queries/documents/offer.ts`
    - [ ] Enhanced listing query with brand colors and complete data
    - [ ] Comprehensive detail query with all field groups
    - [ ] Filtered offers query with dynamic parameters
    - [ ] Related offers query with brand/type matching
- [ ] **1.2** Generate updated TypeScript types (`npm run typegen`)
- [ ] **1.3** Create enhanced OfferCard component in `components/offers/`
    - [ ] Using Card, CardHeader, CardContent, CardFooter components
    - [ ] Brand color integration with CSS custom properties
    - [ ] Badge components for offer types and status
    - [ ] Progress component for redemption tracking
- [ ] **1.4** Update offers listing page (`src/app/(frontend)/offers/page.tsx`)
    - [ ] Enhanced data fetching with new queries
    - [ ] Grid layout with shadcn Card components
    - [ ] Integration with filtering system
- [ ] **1.5** Create comprehensive offer detail page (`src/app/(frontend)/offers/[slug]/page.tsx`)
    - [ ] 12-column layout following brand hub pattern
    - [ ] Brand color CSS custom properties integration
    - [ ] Mobile navigation with collapsible sidebar
    - [ ] SEO metadata integration

### **Phase 2: Advanced Features (Week 2)**
- [ ] **2.1** Financial Calculator component using shadcn components
    - [ ] Card container with CardHeader and CardContent
    - [ ] Input, Select, and Slider components for controls
    - [ ] Real-time calculation updates with brand-colored results
    - [ ] Alert component for disclaimers
- [ ] **2.2** Eligibility Checker component
    - [ ] Accordion component for expandable criteria
    - [ ] Checkbox components for requirements
    - [ ] Badge components for target audience
    - [ ] Dynamic validation with shadcn feedback
- [ ] **2.3** Advanced Filtering system using shadcn components
    - [ ] Card container for filter panel
    - [ ] Select components for dropdown filters
    - [ ] Checkbox components for feature toggles
    - [ ] Separator components for visual organization
- [ ] **2.4** Brand Integration enhancements
    - [ ] Dynamic brand color application following existing patterns
    - [ ] getBrandColor utility integration
    - [ ] Consistent brand styling across all shadcn components
- [ ] **2.5** Tabs system for offer details
    - [ ] Tabs, TabsList, TabsTrigger, TabsContent components
    - [ ] Overview, Benefits, Eligibility, Calculator, Contact tabs
    - [ ] Integrated components within each tab

### **Phase 3: Rich Content & Polish (Week 3)**
- [ ] **3.1** Rich Text Integration
    - [ ] Enhanced portable text components with brand styling
    - [ ] Extension of existing portable text system
    - [ ] Brand-aware link styling and headings
- [ ] **3.2** Widget Components using shadcn
    - [ ] OfferWidget with compact/card/horizontal variants
    - [ ] Card-based layout with flexible content
    - [ ] Badge integration for status indicators
    - [ ] Button integration for actions
- [ ] **3.3** Carousel Component
    - [ ] Card container for offer carousel
    - [ ] Select component for filtering
    - [ ] Horizontal scrolling with shadcn components
- [ ] **3.4** Contact Integration
    - [ ] Card-based contact information display
    - [ ] Button components for call-to-actions
    - [ ] Separator components for content organization
- [ ] **3.5** Benefits Showcase
    - [ ] Card-based benefits display
    - [ ] Badge components for benefit categories
    - [ ] Accordion components for detailed descriptions

### **Phase 4: Widgets & Optimization (Week 4)**
- [ ] **4.1** Reusable Widget Components (`components/offers/`)
    - [ ] OfferWidget with compact/card/horizontal variants (using Card, Badge, Button)
    - [ ] OffersCarousel for homepage/related content (using Card, Select)
    - [ ] Compact offer displays for sidebars (using Card primitives)
    - [ ] Brand-aware styling throughout using existing brand integration
- [ ] **4.2** SEO & Performance Optimization
    - [ ] Complete metadata implementation with structured data
    - [ ] Image optimization with blur placeholders (using existing patterns)
    - [ ] Lazy loading implementation for images and components
    - [ ] Bundle size optimization and code splitting
- [ ] **4.3** Accessibility Implementation (Leveraging Shadcn)
    - [ ] WCAG 2.1 AA compliance audit (shadcn components are pre-compliant)
    - [ ] Keyboard navigation support (using Radix primitives)
    - [ ] Screen reader compatibility (aria labels and descriptions)
    - [ ] Color contrast validation with brand colors
    - [ ] Focus management using shadcn focus patterns
- [ ] **4.4** Mobile Optimization
    - [ ] Responsive design refinement (using shadcn responsive patterns)
    - [ ] Touch interaction improvements (using Radix touch-friendly components)
    - [ ] Mobile navigation patterns (using Sheet, Collapsible)
    - [ ] Performance optimization on mobile devices
- [ ] **4.5** Error Handling & Loading States
    - [ ] Comprehensive error boundaries with Alert components
    - [ ] Loading skeletons using Skeleton component
    - [ ] Empty state handling with custom empty state cards
    - [ ] Offline fallbacks using shadcn feedback patterns
    - [ ] Error recovery mechanisms with user-friendly messaging

### **Phase 5: Testing & Documentation (Week 5)**
- [ ] **5.1** Component Testing
    - [ ] Unit tests for all major components
    - [ ] Integration tests for data flow
    - [ ] Visual regression testing
    - [ ] Cross-browser compatibility
- [ ] **5.2** Performance Testing
    - [ ] Lighthouse audits
    - [ ] Core Web Vitals optimization
    - [ ] Bundle analysis
    - [ ] Image optimization validation
- [ ] **5.3** User Testing
    - [ ] Usability testing sessions
    - [ ] Mobile device testing
    - [ ] Accessibility testing with screen readers
    - [ ] Cross-device compatibility
- [ ] **5.4** Documentation
    - [ ] Component API documentation
    - [ ] Implementation guidelines
    - [ ] Brand integration guide
    - [ ] Maintenance procedures
- [ ] **5.5** Deployment Preparation
    - [ ] Production build optimization
    - [ ] Environment configuration
    - [ ] Analytics integration
    - [ ] Monitoring setup

## 🔧 **Technical Requirements**

### **Dependencies to Add**
```json
{
  "dependencies": {
    "@mux/mux-player-react": "^2.5.0",
    "@portabletext/react": "^3.0.11",
    "date-fns": "^2.30.0"
  }
}
```

**Note**: Most shadcn components are already available. Only adding essential dependencies for video and date handling. All UI components (Card, Tabs, Badge, Button, Select, etc.) are already implemented with brand integration support.

### **Quality Standards**
1. **SOLID Principles**: Single responsibility, dependency injection, clear interfaces
2. **Type Safety**: Perfect schema-to-TypeScript alignment with generated types
3. **Shadcn Integration**: Leverage existing UI component library for consistency
4. **Brand Integration**: Use existing getBrandColor utility and brand styling patterns
5. **Accessibility**: Built-in WCAG compliance through Radix primitives
6. **Performance**: Optimized components with minimal overhead
7. **Maintainability**: Standard component patterns with clear documentation

### **Key Implementation Rules**
- **Always use shadcn components** instead of creating custom primitives
- **Follow existing brand patterns** using getBrandColor utility and className="brand"
- **Extend portable text components** rather than duplicating functionality
- **Leverage Radix accessibility** built into shadcn components
- **Use CSS custom properties** for dynamic brand color integration
- **Maintain consistency** with existing component architecture

## 🎯 **Success Criteria**

The implementation will be considered successful when:
1. **Data Utilization**: All 17 schema field groups are properly leveraged in the frontend
2. **Component Quality**: All components use shadcn primitives with brand integration
3. **User Experience**: Rich, interactive experience showcasing all offer features
4. **Performance**: Fast loading times with optimized shadcn components
5. **Accessibility**: Full WCAG 2.1 AA compliance through Radix primitives
6. **Brand Integration**: Seamless brand color integration using existing utilities
7. **Mobile Experience**: Excellent responsive design using shadcn responsive patterns

This comprehensive plan ensures the offers system will provide a professional, feature-rich experience that fully leverages both the sophisticated Sanity schema and the existing shadcn component infrastructure while maintaining the highest code quality standards.