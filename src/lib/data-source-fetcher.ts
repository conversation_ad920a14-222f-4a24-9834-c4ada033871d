import { client } from "@/sanity/lib/client";
import type { TypedObject } from "@sanity/types";

export interface DataSourceConfig {
  sourceType: "static" | "documents" | "api" | "groq";
  outputFormat: "number" | "documents" | "text" | "raw";
  staticConfig?: {
    value: string;
  };
  documentConfig?: {
    documentType: string;
    customDocumentType?: string;
    filters?: Array<{
      field: string;
      operator: string;
      value: string;
    }>;
    sortBy?: {
      field: string;
      order: "asc" | "desc";
    };
    limit?: number;
    projection?: string;
  };
  apiConfig?: {
    endpoint: string;
    method: "GET" | "POST";
    headers?: Array<{ key: string; value: string }>;
    requestBody?: string;
    responseTransform?: {
      dataPath?: string;
      transform?: string;
    };
    cacheConfig?: {
      enabled: boolean;
      duration: number;
    };
  };
  groqConfig?: {
    query: string;
    params?: Array<{
      key: string;
      value: string;
      type: "string" | "number" | "boolean" | "reference";
    }>;
  };
  errorHandling?: {
    fallbackValue: string;
    retryAttempts: number;
    timeout: number;
  };
}

export interface GridItem {
  _key: string;
  _type: string;
  _id?: string;
  title?: string;
  description?: { _type: string; richTextContent: TypedObject[] } | string;
  shortDescription?: string;
  excerpt?: string;
  content?: never[];
  image?: {
    asset?: {
      url: string;
      _id?: string;
    } | null;
    alt?: string;
    altText?: string;
    caption?: string;
  } | null;
  link?: {
    url: string;
    label?: string;
    openInNewTab?: boolean;
  };
  slug?: string;
  category?:
    | string
    | {
        _id: string;
        title: string;
        slug: string;
      };
  categories?:
    | string[]
    | Array<{
        _id: string;
        title: string;
        slug: string;
      }>;
  tags?: string[];
  publishedAt?: string;
  _createdAt?: string;
  author?:
    | string
    | {
        _id: string;
        name: string;
        username?: string;
        avatar?: {
          asset?: {
            url: string;
          };
        };
      };
  metadata?: {
    status?: string;
    priority?: number;
  };
  featured?: boolean;
  // Additional fields for different content types
  jobTitle?: string;
  company?: string;
  quote?: string;
  avatar?: never;
  logo?: never;
  name?: string;
  username?: string;
  bio?: string;
}

function buildGroqQuery(config: DataSourceConfig["documentConfig"]): string {
  if (!config) return "";

  const documentType = config.customDocumentType || config.documentType;
  let query = `*[_type == "${documentType}"`;

  // Add filters
  if (config.filters && config.filters.length > 0) {
    config.filters.forEach((filter) => {
      if (filter.operator === "match") {
        query += ` && ${filter.field} match "${filter.value}"`;
      } else if (filter.operator === "in") {
        query += ` && ${filter.field} in [${filter.value}]`;
      } else if (filter.operator === "references") {
        query += ` && references("${filter.value}")`;
      } else {
        const value = isNaN(Number(filter.value))
          ? `"${filter.value}"`
          : filter.value;
        query += ` && ${filter.field} ${filter.operator} ${value}`;
      }
    });
  }

  query += "]";

  // Add sorting
  if (config.sortBy?.field) {
    query += ` | order(${config.sortBy.field} ${config.sortBy.order || "desc"})`;
  }

  // Add limit
  if (config.limit) {
    query += ` [0...${config.limit}]`;
  }

  // Add projection with comprehensive fields for different content types
  const projection = config.projection || getDefaultProjection(documentType);
  query += ` ${projection}`;

  return query;
}

function getDefaultProjection(documentType: string): string {
  const baseFields = `
    _id,
    _type,
    _key,
    _createdAt,
    title,
    description,
    excerpt,
    shortDescription,
    content,
    publishedAt
  `;

  const imageFields = `
    image {
      asset->{
        _id,
        url
      },
      alt,
      altText,
      caption
    }
  `;

  const mainImageFields = `
    "image": mainImage {
      asset->{
        _id,
        url
      },
      alt,
      altText,
      caption
    }
  `;

  const categoryFields = `
    category->{
      _id,
      title,
      "slug": slug.current
    },
    categories[]->{
      _id,
      title,
      "slug": slug.current
    }
  `;

  const authorFields = `
    author->{
      _id,
      name,
      username,
      bio,
      avatar {
        asset->{
          url
        }
      }
    }
  `;

  const slugField = `"slug": slug.current`;

  switch (documentType) {
    case "post":
      return `{
        ${baseFields},
        ${imageFields},
        ${categoryFields},
        ${authorFields},
        ${slugField},
        tags,
        featured
      }`;

    case "project":
      return `{
        ${baseFields},
        ${imageFields},
        ${categoryFields},
        ${slugField},
        tags,
        featured,
        client,
        technologies,
        projectUrl,
        githubUrl
      }`;

    case "service":
      return `{
        ${baseFields},
        ${imageFields},
        ${slugField},
        features,
        pricing,
        featured
      }`;

    case "testimonial":
      return `{
        _id,
        _type,
        _key,
        name,
        jobTitle,
        company,
        quote,
        avatar {
          asset->{
            url
          }
        },
        logo {
          asset->{
            url
          }
        }
      }`;

    case "author":
      return `{
        _id,
        _type,
        _key,
        name,
        username,
        bio,
        jobTitle,
        company,
        avatar {
          asset->{
            url
          }
        },
        social
      }`;

    case "offer":
      return `{
        ${baseFields},
        ${mainImageFields},
        ${categoryFields},
        ${slugField},
        tags,
        featured,
        brand,
        offerType,
        validityPeriod,
        conditions,
        applicableModels,
        cta,
        seo
      }`;

    default:
      return `{
        ${baseFields},
        ${imageFields},
        ${slugField}
      }`;
  }
}

function normalizeGridItems(
  data: GridItem[],
  documentType: string,
): GridItem[] {
  return data.map((item, index) => {
    const normalizedItem: GridItem = {
      _key: item._key || item._id || `item-${index}`,
      _type: item._type || documentType,
      _id: item._id,
      title: item.title || item.name,
      shortDescription: item.shortDescription,
      description: item.description || item.excerpt || item.bio,
      content: item.content,
      image: item.image || item.avatar,
      publishedAt: item.publishedAt || item._createdAt,
      author: item.author,
      category: item.category,
      categories: item.categories,
      tags: item.tags,
      featured: item.featured,
      metadata: item.metadata,
    };

    // Handle slug generation for link
    if (item.slug) {
      normalizedItem.link = {
        url: getSlugUrl(documentType, item.slug),
        label: "Plus d'information",
        openInNewTab: false,
      };
    }

    // Handle different content types
    if (documentType === "testimonial") {
      normalizedItem.name = item.name;
      normalizedItem.jobTitle = item.jobTitle;
      normalizedItem.company = item.company;
      normalizedItem.quote = item.quote;
      normalizedItem.description = item.quote;
      normalizedItem.logo = item.logo;
    }

    if (documentType === "author") {
      normalizedItem.name = item.name;
      normalizedItem.username = item.username;
      normalizedItem.jobTitle = item.jobTitle;
      normalizedItem.company = item.company;
      normalizedItem.bio = item.bio;
      normalizedItem.description = item.bio;
    } // Normalize category data
    if (
      normalizedItem.category &&
      typeof normalizedItem.category === "object"
    ) {
      normalizedItem.categories = [normalizedItem.category.title];
    }
    if (normalizedItem.categories && Array.isArray(normalizedItem.categories)) {
      normalizedItem.categories = normalizedItem.categories
        .map((cat) => (typeof cat === "object" ? cat.title : cat))
        .filter(Boolean);
    }

    return normalizedItem;
  });
}

function getSlugUrl(documentType: string, slug: string): string {
  switch (documentType) {
    case "post":
      return `/blog/${slug}`;
    case "project":
      return `/projects/${slug}`;
    case "service":
      return `/services/${slug}`;
    case "offer":
      return `/offres/${slug}`;
    case "brand":
      return `/gammes/${slug}`;
    case "b2bSolution":
      return `/solutions-pro/${slug}`;
    case "page":
      return `/${slug}`;
    default:
      return `/${documentType}/${slug}`;
  }
}

export async function fetchDataSource(
  config: DataSourceConfig,
): Promise<GridItem[]> {
  try {
    switch (config.sourceType) {
      case "static":
        if (config.staticConfig?.value) {
          try {
            // Try to parse as JSON array first
            const parsed = JSON.parse(config.staticConfig.value);
            if (Array.isArray(parsed)) {
              return normalizeGridItems(parsed, "static");
            }
            // If it's a single object, wrap in array
            return normalizeGridItems([parsed], "static");
          } catch {
            // If not JSON, treat as single text item
            return [
              {
                _key: "static-1",
                _type: "static",
                title: config.staticConfig.value,
                description: config.staticConfig.value,
              },
            ];
          }
        }
        return [];

      case "documents":
        if (config.documentConfig) {
          const query = buildGroqQuery(config.documentConfig);
          console.log("Generated GROQ query:", query);
          const data = await client.fetch(query);
          console.log("Fetched data:", data);
          return normalizeGridItems(
            Array.isArray(data) ? data : [data],
            config.documentConfig.documentType,
          );
        }
        return [];

      case "groq":
        if (config.groqConfig?.query) {
          const params: Record<string, never> = {};
          config.groqConfig.params?.forEach((param) => {
            let value: string | number | boolean = param.value;
            switch (param.type) {
              case "number":
                value = Number(param.value);
                break;
              case "boolean":
                value = param.value === "true";
                break;
              case "reference":
              case "string":
              default:
                value = param.value;
                break;
            }
            params[param.key] = value as never;
          });

          const data = await client.fetch(config.groqConfig.query, params);
          return normalizeGridItems(
            Array.isArray(data) ? data : [data],
            "custom",
          );
        }
        return [];

      case "api":
        if (config.apiConfig?.endpoint) {
          const headers: Record<string, string> = {};
          config.apiConfig.headers?.forEach((header) => {
            headers[header.key] = header.value;
          });

          const fetchOptions: RequestInit = {
            method: config.apiConfig.method || "GET",
            headers: {
              "Content-Type": "application/json",
              ...headers,
            },
          };

          if (
            config.apiConfig.method === "POST" &&
            config.apiConfig.requestBody
          ) {
            fetchOptions.body = config.apiConfig.requestBody;
          }

          const response = await fetch(config.apiConfig.endpoint, fetchOptions);
          let data = await response.json();

          // Apply data path transformation
          if (config.apiConfig.responseTransform?.dataPath) {
            const path = config.apiConfig.responseTransform.dataPath.split(".");
            for (const key of path) {
              data = data?.[key];
            }
          }

          // Apply custom transformation
          if (config.apiConfig.responseTransform?.transform) {
            try {
              const transformFn = new Function(
                "data",
                config.apiConfig.responseTransform.transform,
              );
              data = transformFn(data);
            } catch (error) {
              console.warn("Data transformation failed:", error);
            }
          }

          return normalizeGridItems(Array.isArray(data) ? data : [data], "api");
        }
        return [];

      default:
        return [];
    }
  } catch (error) {
    console.error("Data source fetch failed:", error);

    // Return fallback if configured
    if (config.errorHandling?.fallbackValue) {
      try {
        const parsed = JSON.parse(config.errorHandling.fallbackValue);
        return normalizeGridItems(
          Array.isArray(parsed) ? parsed : [parsed],
          "fallback",
        );
      } catch {
        return [
          {
            _key: "fallback-1",
            _type: "fallback",
            title: "Content unavailable",
            description: config.errorHandling.fallbackValue,
          },
        ];
      }
    }

    return [];
  }
}
