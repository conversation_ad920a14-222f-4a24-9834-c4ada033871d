/**
 * Navigation layout detection utilities
 * Determines which custom navigation layout to use based on page reference types
 */

export type NavigationLayoutType = 'brand' | null;

/**
 * Determines the navigation layout type based on page reference
 * @param pageReference - Page reference object with _type
 * @returns Navigation layout type or null for default layout
 */
export function getNavigationLayoutType(pageReference?: { _type?: string }): NavigationLayoutType {
  if (!pageReference?._type) return null;
  
  switch (pageReference._type) {
    case 'brandsPage':
      return 'brand';
    // Future: Add more custom layouts here
    // case 'servicesPage':
    //   return 'services';
    // case 'projectsPage':
    //   return 'projects';
    default:
      return null;
  }
}

/**
 * Checks if any page references in a group have a custom layout
 * @param pageReferences - Array of page references
 * @returns Navigation layout type or null
 */
export function getGroupNavigationLayoutType(
  pageReferences?: Array<{ _type?: string }>
): NavigationLayoutType {
  if (!pageReferences?.length) return null;
  
  // Check if any reference has a custom layout
  for (const pageRef of pageReferences) {
    const layoutType = getNavigationLayoutType(pageRef);
    if (layoutType) return layoutType;
  }
  
  return null;
}
