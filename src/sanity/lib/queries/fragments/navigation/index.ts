import { defineQuery } from "next-sanity";

// Brand navigation queries for enhanced navigation layouts

export const brandNavigationGridQuery = defineQuery(`
  *[_type == 'brand' && displaySettings.isActive == true] 
  | order(displaySettings.navigationOrder asc, name asc) {
    _id,
    name,
    'slug': slug.current,
    tagline,
    logo { asset->{...} },
    brandColors { primaryColor, secondaryColor, accentColor }
  }
`);

export const brandFeaturedModelsQuery = defineQuery(`
  *[_type == 'vehicleModel' 
    && brand._ref == $brandId 
    && displaySettings.isActive == true 
    && displaySettings.isFeatured == true
  ][0...3] {
    _id,
    name,
    'slug': slug.current,
    modelYear,
    mainImage
  }
`);

export const brandFeaturedOffersQuery = defineQuery(`
  *[_type == 'offer' 
    && brand._ref == $brandId 
    && displaySettings.isActive == true 
    && displaySettings.isFeatured == true
  ][0...2] {
    _id,
    title,
    'slug': slug.current,
    offerType,
    savings
  }
`);
