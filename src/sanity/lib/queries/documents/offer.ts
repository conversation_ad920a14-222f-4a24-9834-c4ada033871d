import { defineQuery } from "next-sanity";
import { pageBuilder } from "../fragments/page-builder";

export const offerSlugsQuery =
  defineQuery(`*[_type == "offer" && defined(slug.current)] {
  'params': { 'slug': slug.current }
}`);

export const offersPageQuery = defineQuery(`*[_type == 'offersPage'][0] {
  _id,
  _type,
  title,
  'slug': slug.current,
  ${pageBuilder},
  "offers": *[_type == 'offer' && displaySettings.isActive == true] 
  | order(displaySettings.priority asc, displaySettings.isFeatured desc, _createdAt desc) {
    _id,
    _type,
    title,
    'slug': slug.current,
    offerType,
    shortDescription,
    
    // Brand integration with colors
    brand->{
      _id,
      name,
      "slug": slug.current,
      brandColors,
      logo
    },
    
    // Optimized visual assets
    mainImage{
      asset->{
        _id,
        url,
        metadata{dimensions, blurHash}
      },
      altText,
      caption,
      hotspot,
      crop
    },
    
    // Key benefits preview
    keyBenefits[0...3]{
      title,
      description,
      icon{asset->{url}, altText}
    },
    
    // Validity and urgency
    validityPeriod{
      isLimitedTime,
      validFrom,
      validTo
    },
    
    // Financial highlights
    financingDetails{
      financingType,
      interestRate,
      monthlyPayment,
      currency
    },
    
    // Savings information
    savings{
      discountType,
      discountValue,
      discountPercentage,
      rebateAmount
    },
    
    // Display settings
    displaySettings{
      isFeatured,
      showOnHomepage,
      priority
    },
    
    // Redemption tracking
    maxRedemptions,
    currentRedemptions,
    
    // SEO
    seo{
      title,
      description,
      noIndex,
      image
    }
  },
  "seo": {
    "title": coalesce(seo.title, title, ""),
    "description": coalesce(seo.description,  ""),
    "noIndex": seo.noIndex == true,
    "image": seo.image,
  },
}`);

export const offerBySlugQuery =
  defineQuery(`*[_type == 'offer' && slug.current == $slug][0] {
  _id,
  _type,
  title,
  "slug": slug.current,
  offerCode,
  offerType,
  shortDescription,
  description,
  
  // Complete brand information
  brand->{
    _id,
    name,
    "slug": slug.current,
    brandColors,
    logo,
    description,
    contactInformation
  },
  
  // All visual assets
  mainImage{
    asset->{
      _id,
      url,
      metadata{dimensions, blurHash, palette}
    },
    altText,
    caption,
    hotspot,
    crop
  },
  
  gallery[]{
    asset->{_id, url, metadata{dimensions, blurHash}},
    altText,
    caption,
    hotspot,
    crop
  },
  
  offerVideo{
    asset->{assetId, playbackId, status}
  },
  
  // Complete benefits
  keyBenefits[]{
    title,
    description,
    icon{asset->{url}, altText}
  },
  
  // Validity details
  validityPeriod{
    validFrom,
    validTo,
    isLimitedTime
  },
  maxRedemptions,
  currentRedemptions,
  
  // Eligibility
  targetAudience[],
  eligibilityCriteria[]{
    title,
    description,
    isRequired
  },
  
  // Vehicle application
  applicableModels[]->{
    _id,
    name,
    "slug": slug.current,
    brand->{name, "slug": slug.current},
    category,
    marketSegment,
    modelYear
  },
  
  // Complete financial details
  financingDetails{
    financingType,
    interestRate,
    duration,
    downPayment,
    monthlyPayment,
    finalPayment,
    totalAmount,
    currency
  },
  
  // Savings and discounts
  savings{
    discountType,
    discountValue,
    discountPercentage,
    maximumDiscount,
    rebateAmount,
    tradeInBonus,
    loyaltyBonus,
    referralBonus
  },
  
  // Promotional pricing
  promotionalPricing{
    originalPrice,
    promotionalPrice,
    pricingNotes
  },
  
  // Contact and application
  contactInformation{
    contactPerson,
    contactRole,
    contactPhone,
    contactEmail,
    contactCenter->{
      _id,
      name,
      address,
      phone,
      email,
      openingHours
    }
  },
  
  onlineApplication{
    isEnabled,
    applicationUrl,
    applicationFormId
  },
  
  // Legal information
  legalDisclaimer,
  termsAndConditions,
  
  // Related offers
  relatedOffers[]->{
    _id,
    title,
    "slug": slug.current,
    offerType,
    shortDescription,
    mainImage{asset->{url}, altText},
    brand->{name, "slug": slug.current}
  },
  
  // Display and SEO
  displaySettings,
  seo{
    title,
    description,
    noIndex,
    image
  }
}`);

export const allOffersQuery =
  defineQuery(`*[_type == 'offer' && displaySettings.isActive == true] 
  | order(displaySettings.priority asc, displaySettings.isFeatured desc, _createdAt desc) {
  _id,
  _type,
  title,
  'slug': slug.current,
  offerType,
  shortDescription,
  
  // Brand integration with colors
  brand->{
    _id,
    name,
    "slug": slug.current,
    brandColors,
    logo
  },
  
  // Optimized visual assets
  mainImage{
    asset->{
      _id,
      url,
      metadata{dimensions, blurHash}
    },
    altText,
    caption,
    hotspot,
    crop
  },
  
  // Key benefits preview
  keyBenefits[0...3]{
    title,
    description,
    icon{asset->{url}, altText}
  },
  
  // Validity and urgency
  validityPeriod{
    isLimitedTime,
    validFrom,
    validTo
  },
  
  // Financial highlights
  financingDetails{
    financingType,
    interestRate,
    monthlyPayment,
    currency
  },
  
  // Savings information
  savings{
    discountType,
    discountValue,
    discountPercentage,
    rebateAmount
  },
  
  // Display settings
  displaySettings{
    isFeatured,
    showOnHomepage,
    priority
  },
  
  // Redemption tracking
  maxRedemptions,
  currentRedemptions
}`);

export const filteredOffersQuery = defineQuery(`*[_type == "offer"
  && displaySettings.isActive == true
  && ($offerType == null || offerType == $offerType)
  && ($brandSlug == null || brand->slug.current == $brandSlug)
  && ($modelSlug == null || $modelSlug in applicableModels[]->slug.current)
  && ($isLimitedTime == null || validityPeriod.isLimitedTime == $isLimitedTime)
  && ($isFeatured == null || displaySettings.isFeatured == $isFeatured)
  && ($hasFinancing == null || financingDetails != null)
] | order(
  displaySettings.priority asc,
  displaySettings.isFeatured desc,
  _createdAt desc
) [$offset...$limit] {
  _id,
  _type,
  title,
  "slug": slug.current,
  offerType,
  shortDescription,

  // Brand integration with colors
  brand->{
    _id,
    name,
    "slug": slug.current,
    brandColors,
    logo
  },

  // Optimized visual assets
  mainImage{
    asset->{
      _id,
      url,
      metadata{dimensions, blurHash}
    },
    altText,
    caption,
    hotspot,
    crop
  },

  // Key benefits preview
  keyBenefits[0...3]{
    title,
    description,
    icon{asset->{url}, altText}
  },

  // Validity and urgency
  validityPeriod{
    isLimitedTime,
    validFrom,
    validTo
  },

  // Financial highlights
  financingDetails{
    financingType,
    interestRate,
    monthlyPayment,
    currency
  },

  // Savings information
  savings{
    discountType,
    discountValue,
    discountPercentage,
    rebateAmount
  },

  // Display settings
  displaySettings{
    isFeatured,
    showOnHomepage,
    priority
  },

  // Redemption tracking
  maxRedemptions,
  currentRedemptions,

  // Vehicle application
  applicableModels[]->{
    _id,
    name,
    "slug": slug.current,
    brand->{name, "slug": slug.current}
  }
}`);
