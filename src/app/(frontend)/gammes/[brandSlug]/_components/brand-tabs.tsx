"use client";
import React from "react";
import { Info } from "lucide-react";
import BrandOverview from "./brand-overview";
import BrandOffers from "./brand-offers";
import VehicleModelGrid from "@/components/brands/models/grid/vehicle-model-grid";
import { Tabs, <PERSON><PERSON><PERSON>ist, Ta<PERSON><PERSON>rigger, TabsContent } from "@/components/ui/tabs";
import type {
  BrandBySlugQueryResult,
  VehicleModelsByBrandQueryResult,
  AllOffersQueryResult,
} from "../../../../../../sanity.types";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { useSearchParams } from "next/navigation";
import { getBrandColor } from "@/components/brands/models/utils";

interface BrandTabsProps {
  brand: BrandBySlugQueryResult;
  vehicleModels?: VehicleModelsByBrandQueryResult;
  offers?: AllOffersQueryResult;
}

type TabType = "overview" | "models" | "leasing" | "service" | "offers";

export default function BrandTabs({ brand, vehicleModels, offers }: BrandTabsProps) {
  const searchParams = useSearchParams();
  const initialTab = (searchParams?.get("tab") as TabType) || "overview";
  const brandColor = getBrandColor(brand?.brandColors?.primaryColor);

  const tabs = [
    {
      id: "overview" as TabType,
      label: "OVERVIEW",
      description: "Brand story & heritage",
    },
    {
      id: "models" as TabType,
      label: "MODELS",
      description: "Vehicle lineup",
    },
    {
      id: "leasing" as TabType,
      label: "LEASING",
      description: "Leasing options",
    },
    {
      id: "service" as TabType,
      label: "SERVICE",
      description: "Service & support",
    },
    {
      id: "offers" as TabType,
      label: "OFFERS",
      description: "Special offers & promotions",
    },
  ];

  return (
    <Tabs defaultValue={initialTab} className="space-y-8">
      {/* Tab Navigation */}
      <div className="border-b border-dashed overflow-x-auto pb-2">
        <TabsList className="flex gap-6 pb-1 h-auto bg-transparent p-0">
          {tabs.map((tab) => (
            <TabsTrigger
              key={tab.id}
              value={tab.id}
              className="py-4 text-left transition-colors duration-200 flex-none data-[state=active]:bg-muted/50 dark:data-[state=active]:bg-muted/50 data-[state=active]:shadow-none data-[state=inactive]:border-transparent data-[state=active]:!border-b-[var(--brand-color-primary)]"
              onClick={() => {
                const url = new URL(window.location.href);
                url.searchParams.set("tab", tab.id);
                window.history.pushState({}, "", url.toString());
              }}
            >
              <div className="flex flex-col items-start">
                <div className="text-sm font-medium tracking-wide">
                  {tab.label}
                </div>
                <div className="text-xs text-muted-foreground mt-0.5">
                  {tab.description}
                </div>
              </div>
            </TabsTrigger>
          ))}
        </TabsList>
      </div>

      {/* Tab Content */}
      <div className="min-h-[400px]">
        <TabsContent value="overview">
          <BrandOverview brand={brand} />
        </TabsContent>

        <TabsContent value="models">
          {vehicleModels && (
            <div className="space-y-6">
              <div className="py-4 border-b border-dashed">
                <h2 className="text-xl font-bold mb-2">{brand?.name} Models</h2>
                <p className="text-muted-foreground">
                  Complete range of {brand?.name} vehicles.
                </p>
              </div>
              <VehicleModelGrid
                vehicleModels={vehicleModels}
                prioritizeFirst={true}
                variant="compact"
                columns={{
                  mobile: 1,
                  tablet: 1,
                  desktop: 2,
                }}
              />
              <div className="flex justify-center items-center">
                <Link
                  href={`/gammes/${brand?.slug}/modeles`}
                  style={{ borderColor: brandColor, color: brandColor }}
                >
                  <Button variant="outline">View All Models</Button>
                </Link>
              </div>
            </div>
          )}
        </TabsContent>

        <TabsContent value="leasing">
          <div className="space-y-6">
            <div className="text-center py-12">
              <div className="text-muted-foreground">
                <Info className="w-8 h-8 mx-auto mb-2" />
                <p>Leasing information will be available soon.</p>
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="service">
          <div className="space-y-6">
            <div className="text-center py-12">
              <div className="text-muted-foreground">
                <Info className="w-8 h-8 mx-auto mb-2" />
                <p>Service information will be available soon.</p>
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="offers">
          <BrandOffers brand={brand} offers={offers} />
        </TabsContent>
      </div>
    </Tabs>
  );
}
