import type { <PERSON>ada<PERSON> } from "next";
import { sanityFetch } from "@/sanity/lib/live";
import { processMetadata } from "@/lib/utils";
import { notFound, redirect } from "next/navigation";
import {
  vehicleModelBySlugQuery,
  vehicleModelSlugsQuery,
} from "@/sanity/lib/queries/documents/vehicleModel";
import { filteredOffersQuery } from "@/sanity/lib/queries/documents/offer";
import VehicleModelHero from "./_components/vehicle-model-hero";
import VehicleModelNavigation from "./_components/vehicle-model-navigation";
import VehicleModelQuickActions from "./_components/vehicle-model-quick-actions";
import VehicleModelTabs from "./_components/vehicle-model-tabs";
import BackButton from "@/components/shared/back-button";
import type {
  BrandSlugsQueryResult,
  VehicleModelBySlugQueryResult,
  VehicleModelSlugsQueryResult,
  AllOffersQueryResult,
} from "../../../../../../../sanity.types";
import {
  She<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { brandSlugsQuery } from "@/sanity/lib/queries/documents/brand";
import { Suspense } from "react";
import { stegaClean } from "next-sanity";
import { Button } from "@/components/ui/button";

interface VehicleModelPageProps {
  params: Promise<{ brandSlug: string; modelSlug: string }>;
}

export async function generateStaticParams() {
  const { data: vehicleModels }: { data: VehicleModelSlugsQueryResult } =
    await sanityFetch({
      query: vehicleModelSlugsQuery,
      perspective: "published",
      stega: false,
    });

  return vehicleModels.map((model) => ({
    brandSlug: model.params.brandSlug,
    modelSlug: model.params.modelSlug,
  }));
}

export async function generateMetadata({
  params,
}: VehicleModelPageProps): Promise<Metadata> {
  const paramsData = await params;
  const { data: vehicleModel } = await sanityFetch({
    query: vehicleModelBySlugQuery,
    params: {
      brand: paramsData.brandSlug,
      slug: paramsData.modelSlug,
    },
    stega: false,
  });

  if (!vehicleModel) {
    return {};
  }

  return processMetadata({ data: vehicleModel });
}

export default async function VehicleModelPage({
  params,
}: VehicleModelPageProps) {
  const paramsData = await params;

  const [vehicleModelResult, brandsResult, offersResult] = await Promise.all([
    sanityFetch({
      query: vehicleModelBySlugQuery,
      params: {
        brand: paramsData.brandSlug,
        slug: paramsData.modelSlug,
      },
    }),
    sanityFetch({
      query: brandSlugsQuery,
      perspective: "published",
    }),
    sanityFetch({
      query: filteredOffersQuery,
      params: {
        brandSlug: paramsData.brandSlug,
        modelSlug: paramsData.modelSlug,
        offerType: null,
        isLimitedTime: null,
        isFeatured: null,
        hasFinancing: null,
        offset: 0,
        limit: 50,
      },
    }),
  ]);

  const { data: vehicleModel } = vehicleModelResult;
  const { data: brands }: { data: BrandSlugsQueryResult[] } = brandsResult;
  const { data: offers }: { data: AllOffersQueryResult } = offersResult;

  if (!vehicleModel) {
    if (
      brands.find(
        (brand) =>
          (brand as unknown as BrandSlugsQueryResult[number])?.params.slug ===
          paramsData.brandSlug,
      )
    ) {
      return redirect(`/gammes/${paramsData.brandSlug}`);
    }

    notFound();
  }

  const typedVehicleModel = vehicleModel as VehicleModelBySlugQueryResult;

  const backLink = typedVehicleModel?.brand?.slug
    ? `/gammes/${typedVehicleModel?.brand?.slug}`
    : undefined;

  return (
    <div
      className="relative grid grid-cols-12 gap-y-10 xl:gap-20 max-w-10xl mx-auto pt-8 lg:pt-0"
      style={{
        "--brand-color-primary": stegaClean(
          typedVehicleModel?.brand?.brandColors?.primaryColor?.value,
        ),
        "--brand-color-secondary": stegaClean(
          typedVehicleModel?.brand?.brandColors?.secondaryColor?.value,
        ),
        "--brand-color-accent": stegaClean(
          typedVehicleModel?.brand?.brandColors?.accentColor?.value,
        ),
      }}
    >
      {/* Mobile Navigation */}
      <div className="fixed block xl:hidden top-18 left-0 right-0 w-full z-50 bg-card/30 backdrop-blur-md px-2">
        <VehicleModelNavigation
          vehicleModel={typedVehicleModel}
          defaultOpen={false}
        />
        <div className="absolute top-8 right-6">
          <BackButton
            href={backLink}
            title={`Back to ${typedVehicleModel?.brand?.name}`}
          />
        </div>
      </div>

      {/* Left Sidebar - Navigation */}
      <aside className="hidden xl:block order-0 xl:order-0 col-span-12 xl:col-span-2 xl:sticky xl:top-28 h-fit -translate-x-1 md:-translate-x-0">
        <BackButton
          href={backLink}
          title={`Back to ${typedVehicleModel?.brand?.name}`}
        />
        <VehicleModelNavigation vehicleModel={typedVehicleModel} />
      </aside>

      {/* Main Content */}
      <main className="pt-28 xl:pt-0 order-1 xl:order-1 col-span-12 xl:col-span-7 xl:pl-10 xl:border-l xl:border-dashed">
        {/* Vehicle Hero */}
        <VehicleModelHero
          vehicleModel={typedVehicleModel}
          layout="background"
        />

        {/* Tabbed Content */}
        <div className="mt-10 xl:mt-14">
          <Suspense fallback={<div>Loading...</div>}>
            <VehicleModelTabs vehicleModel={typedVehicleModel} offers={offers} />
          </Suspense>
        </div>
      </main>

      {/* Right Sidebar - Quick Actions */}
      <aside className="hidden xl:block order-2 xl:order-2 col-span-12 xl:col-span-3 xl:sticky xl:top-28 h-fit space-y-5">
        <VehicleModelQuickActions vehicleModel={typedVehicleModel} />
      </aside>

      {/* Mobile Quick Actions */}
      <div className="block xl:hidden fixed bottom-0 left-0 right-0 w-full z-50">
        <Sheet>
          <SheetTrigger asChild>
            <Button
              variant="secondary"
              className="brand w-full h-14 rounded-none"
            >
              Buy Now
            </Button>
          </SheetTrigger>
          <SheetContent side="bottom">
            <SheetHeader>
              <SheetTitle>
                Ready to Drive your {typedVehicleModel?.name}?
              </SheetTitle>
            </SheetHeader>
            <VehicleModelQuickActions
              vehicleModel={typedVehicleModel}
              defaultActionOpen={false}
              defaultSpecsOpen={false}
            />
          </SheetContent>
        </Sheet>
      </div>
    </div>
  );
}
