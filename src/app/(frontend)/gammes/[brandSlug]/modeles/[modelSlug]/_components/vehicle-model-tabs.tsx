"use client";
import React from "react";
import { Info } from "lucide-react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON><PERSON>rigger, Ta<PERSON>Content } from "@/components/ui/tabs";
import VehicleModelOverview from "./vehicle-model-overview";
import VehicleModelSpecifications from "./vehicle-model-specs";
import VehicleModelListings from "./vehicle-model-listings";
import VehicleModelGallery from "./vehicle-model-gallery";
import VehicleModelFeatures from "./vehicle-model-features";
import VehicleModelOffers from "./vehicle-model-offers";
import type {AllOffersQueryResult, VehicleModelBySlugQueryResult} from "../../../../../../../../sanity.types";
import { useSearchParams } from "next/navigation";
import { stegaClean } from "next-sanity";

interface VehicleModelTabsProps {
  vehicleModel: VehicleModelBySlugQueryResult;
  offers?: AllOffersQueryResult;
}

type TabType =
  | "overview"
  | "specifications"
  | "listings"
  | "gallery"
  | "offers"
  | "configure";

export default function VehicleModelTabs({
  vehicleModel,
  offers,
}: VehicleModelTabsProps) {
  const searchParams = useSearchParams();
  const initialTab = (searchParams.get("tab") as TabType) || "overview";
  const brandColor =
    stegaClean(vehicleModel?.brand?.brandColors?.primaryColor?.value) ||
    "#000000";

  const tabs = [
    {
      id: "overview" as TabType,
      label: "OVERVIEW",
      description: "Vehicle details",
    },
    {
      id: "specifications" as TabType,
      label: "TECH SPECIFICATION",
      description: "Technical details",
    },
    {
      id: "listings" as TabType,
      label: "VEHICLES",
      description: "Available models",
    },
    {
      id: "gallery" as TabType,
      label: "GALLERY",
      description: "Photos & videos",
    },
    {
      id: "offers" as TabType,
      label: "OFFERS",
      description: "Special offers & promotions",
    },
    {
      id: "configure" as TabType,
      label: "CONFIGURE",
      description: "Build & price",
    },
  ];

  return (
    <Tabs defaultValue={initialTab} className="space-y-8">
      {/* Tab Navigation */}
      <div className="border-b border-dashed overflow-x-auto pb-2">
        <TabsList className="flex gap-6 pb-1 h-auto bg-transparent p-0">
          {tabs.map((tab) => (
            <TabsTrigger
              key={tab.id}
              value={tab.id}
              className="min-w-30 py-4 text-left transition-colors duration-200 flex-none data-[state=active]:bg-muted/50 dark:data-[state=active]:bg-muted/50 data-[state=active]:shadow-none data-[state=inactive]:border-transparent data-[state=active]:!border-b-[var(--brand-color)]"
              style={
                {
                  "--brand-color": brandColor,
                } as React.CSSProperties
              }
              onClick={() => {
                const url = new URL(window.location.href);
                url.searchParams.set("tab", tab.id);
                window.history.pushState({}, "", url.toString());
              }}
            >
              <div className="flex flex-col items-start pointer-cursor  pointer-events-auto">
                <div className="text-sm font-medium tracking-wide">
                  {tab.label}
                </div>
                <div className="text-xs text-muted-foreground mt-0.5">
                  {tab.description}
                </div>
              </div>
            </TabsTrigger>
          ))}
        </TabsList>
      </div>

      {/* Tab Content */}
      <div className="min-h-[400px]">
        <TabsContent value="overview">
          <VehicleModelOverview vehicleModel={vehicleModel} />
        </TabsContent>

        <TabsContent value="specifications">
          <div className="space-y-6">
            <VehicleModelFeatures vehicleModel={vehicleModel} />
            <VehicleModelSpecifications vehicleModel={vehicleModel} />
          </div>
        </TabsContent>

        <TabsContent value="listings">
          <VehicleModelListings vehicleModel={vehicleModel} />
        </TabsContent>

        <TabsContent value="gallery">
          <VehicleModelGallery vehicleModel={vehicleModel} />
        </TabsContent>

        <TabsContent value="offers">
          <VehicleModelOffers vehicleModel={vehicleModel} offers={offers} />
        </TabsContent>

        <TabsContent value="configure">
          <div className="space-y-6">
            <div className="py-4 border-b border-dashed">
              <h2 className="text-xl font-bold mb-2">
                Configure Your {vehicleModel?.name}
              </h2>
              <p className="text-muted-foreground">
                Build and price your perfect {vehicleModel?.name} configuration.
              </p>
            </div>
            <div className="text-center py-12">
              <div className="text-muted-foreground">
                <Info className="w-8 h-8 mx-auto mb-2" />
                <p>Vehicle configurator coming soon.</p>
                <div className="mt-4">
                  <button
                    className="px-6 py-2 rounded-lg border border-dashed text-sm font-medium hover:bg-card transition-colors"
                    style={{ borderColor: brandColor, color: brandColor }}
                  >
                    Request Configuration
                  </button>
                </div>
              </div>
            </div>
          </div>
        </TabsContent>
      </div>
    </Tabs>
  );
}
