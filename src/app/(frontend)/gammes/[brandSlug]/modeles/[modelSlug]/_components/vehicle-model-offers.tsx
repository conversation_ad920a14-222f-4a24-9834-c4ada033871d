import React from "react";
import { Gift } from "lucide-react";
import OfferCard from "@/components/offers/offer-card";
import type {
  VehicleModelBySlugQueryResult,
  AllOffersQueryResult,
  OfferBySlugQueryResult,
} from "../../../../../../../../sanity.types";
import Link from "next/link";
import { Button } from "@/components/ui/button";

interface VehicleModelOffersProps {
  vehicleModel: VehicleModelBySlugQueryResult;
  offers?: AllOffersQueryResult;
}

export default function VehicleModelOffers({
  vehicleModel,
  offers,
}: VehicleModelOffersProps) {
  if (!vehicleModel) return null;

  // Empty state
  if (!offers || offers.length === 0) {
    return (
      <div className="space-y-8">
        <div className="py-4 border-b border-dashed">
          <h2 className="text-xl font-bold mb-2">
            Current Offers for {vehicleModel.name}
          </h2>
          <p className="text-muted-foreground">
            Special offers and promotions for this vehicle model.
          </p>
        </div>

        <div className="text-center py-12">
          <div className="text-muted-foreground mb-4">
            <Gift className="w-8 h-8 mx-auto mb-2" />
            <p>
              No current offers available for {vehicleModel.brand?.name}{" "}
              {vehicleModel.name}.
            </p>
            <div className="mt-4">
              <Link href="/offres">
                <Button variant="outline-solid" className="brand">
                  Browse All Offers
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header Section */}
      <div className="py-4 border-b border-dashed">
        <h2 className="text-xl font-bold mb-2">
          Current Offers for {vehicleModel.name}
        </h2>
        <p className="text-muted-foreground">
          Discover special offers and promotions available for the{" "}
          {vehicleModel.brand?.name} {vehicleModel.name}.
        </p>
      </div>

      {/* Offers Count */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          {offers?.length || 0} offer{(offers?.length || 0) !== 1 ? "s" : ""}{" "}
          available
        </div>
        <Link href="/offres">
          <Button variant="underline" className="brand text-sm">
            View all offers →
          </Button>
        </Link>
      </div>

      {/* Offers Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-2 gap-6">
        {offers?.map((offer, index) => (
          <div key={offer._id} className="col-span-1">
            <OfferCard
              offer={offer as NonNullable<OfferBySlugQueryResult>}
              priority={index < 6 ? "high" : "normal"}
              variant="grid"
            />
          </div>
        ))}
      </div>

      {/* Call to Action */}
      {offers && offers.length > 0 && (
        <div className="text-center pt-8 border-t border-dashed">
          <p className="text-muted-foreground mb-4">
            Interested in one of these offers?
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/offres">
              <Button className="brand">Browse All Offers</Button>
            </Link>
            <Link href="/contact">
              <Button variant="outline-solid" className="brand">
                Contact Dealer
              </Button>
            </Link>
          </div>
        </div>
      )}
    </div>
  );
}
