import type { Metadata } from "next";
import { sanityFetch } from "@/sanity/lib/live";
import { processMetadata } from "@/lib/utils";
import { notFound } from "next/navigation";
import {
  brandBySlugQuery,
  brandSlugsQuery,
} from "@/sanity/lib/queries/documents/brand";
import { vehicleModelsByBrandQuery } from "@/sanity/lib/queries/documents/vehicleModel";
import { filteredOffersQuery } from "@/sanity/lib/queries/documents/offer";
import BrandHero from "./_components/brand-hero";
import BrandNavigation from "./_components/brand-navigation";
import BrandQuickActions from "./_components/brand-quick-actions";
import BrandTabs from "./_components/brand-tabs";
import BackButton from "@/components/shared/back-button";
import { PageBuilder } from "@/components/page-builder";
import type {
  BrandBySlugQueryResult,
  BrandSlugsQueryResult,
  AllOffersQueryResult,
} from "../../../../../sanity.types";
import { Suspense } from "react";
import { stegaClean } from "next-sanity";

interface BrandPageProps {
  params: Promise<{ brandSlug: string }>;
}

export async function generateStaticParams() {
  const { data: brands }: { data: BrandSlugsQueryResult } = await sanityFetch({
    query: brandSlugsQuery,
    perspective: "published",
    stega: false,
  });

  return brands.map((brand) => ({
    brandSlug: brand.params.slug,
  }));
}

export async function generateMetadata({
  params,
}: BrandPageProps): Promise<Metadata> {
  const paramsData = await params;
  const { data: brand } = await sanityFetch({
    query: brandBySlugQuery,
    params: {
      slug: paramsData.brandSlug,
    },
    stega: false,
  });

  if (!brand) {
    return {};
  }

  return processMetadata({ data: brand });
}

export default async function BrandPage({ params }: BrandPageProps) {
  const paramsData = await params;
  const [brandResult, vehicleModelsResult, offersResult] = await Promise.all([
    sanityFetch({
      query: brandBySlugQuery,
      params: {
        slug: paramsData.brandSlug,
      },
    }),
    sanityFetch({
      query: vehicleModelsByBrandQuery,
      params: {
        slug: paramsData.brandSlug,
      },
    }),
    sanityFetch({
      query: filteredOffersQuery,
      params: {
        brandSlug: paramsData.brandSlug,
        modelSlug: null,
        offerType: null,
        isLimitedTime: null,
        isFeatured: null,
        hasFinancing: null,
        offset: 0,
        limit: 50,
      },
    }),
  ]);

  const { data: brand } = brandResult;
  const { data: vehicleModels } = vehicleModelsResult;
  const { data: offers }: { data: AllOffersQueryResult } = offersResult;

  if (!brand) notFound();

  return (
    <div
      className="grid grid-cols-12 gap-y-10 xl:gap-20 max-w-10xl mx-auto"
      style={{
        "--brand-color-primary": stegaClean(
          brand?.brandColors?.primaryColor?.value,
        ),
        "--brand-color-secondary": stegaClean(
          brand?.brandColors?.secondaryColor?.value,
        ),
        "--brand-color-accent": stegaClean(
          brand?.brandColors?.accentColor?.value,
        ),
      }}
    >
      {/* Mobile Navigation */}
      <div className="fixed block xl:hidden top-18 left-0 right-0 w-full z-50 bg-card/30 backdrop-blur-md px-2">
        <BrandNavigation
          brand={brand as BrandBySlugQueryResult}
          defaultOpen={false}
        />
        <div className="absolute top-8 right-6">
          <BackButton href={"/gammes"} title="Back" />
        </div>
      </div>

      {/* Left Sidebar - Navigation */}
      <aside className="hidden xl:block order-2 lg:order-0 col-span-12 xl:col-span-2 xl:sticky xl:top-28 h-fit -translate-x-1 md:-translate-x-0">
        <BackButton href={"/gammes"} title="Back to Brands" />
        <BrandNavigation brand={brand as BrandBySlugQueryResult} />
      </aside>

      {/* Main Content */}
      <main className="pt-32 xl:pt-0 order-0 lg:order-1 col-span-12 xl:col-span-7 xl:px-10 xl:border-x xl:border-dashed">
        {/* Brand Hero */}
        <BrandHero
          brand={brand as BrandBySlugQueryResult}
          layout="background"
        />

        {/* Tabbed Content */}
        <div className="mt-10 xl:mt-14">
          <Suspense fallback={<div>Loading tabs...</div>}>
            <BrandTabs
              brand={brand as BrandBySlugQueryResult}
              vehicleModels={vehicleModels}
              offers={offers}
            />
          </Suspense>
        </div>

        {/* Page Builder Content */}
        <div className="mt-16 md:mt-24">
          <PageBuilder
            id={brand._id}
            type={brand._type}
            pageBuilder={brand.pageBuilder ?? []}
          />
        </div>
      </main>

      {/* Right Sidebar - Quick Actions */}
      <aside className="order-2 col-span-12 xl:col-span-3 xl:sticky xl:top-28 h-fit space-y-5">
        <BrandQuickActions
          brand={brand as BrandBySlugQueryResult}
          vehicleModels={vehicleModels}
        />
      </aside>
    </div>
  );
}
