import type { Metadata } from "next";
import { notFound } from "next/navigation";
import { getBrandColor } from "@/components/brands/models/utils";
import { processMetadata } from "@/lib/utils";
import { sanityFetch } from "@/sanity/lib/live";
import {
  offerBySlugQuery,
  offerSlugsQuery,
} from "@/sanity/lib/queries/documents/offer";
import OfferNavigation from "../_components/offer-navigation";
import OfferHero from "../_components/offer-hero";
import OfferContent from "../_components/offer-content";
import OfferQuickActions from "../_components/offer-quick-actions";
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetHeader,
  Sheet<PERSON><PERSON>le,
  SheetTrigger,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import type {
  OfferBySlugQueryResult,
  OfferSlugsQueryResult,
} from "../../../../../sanity.types";

interface OfferPageProps {
  params: Promise<{
    slug: string;
  }>;
}

export async function generateStaticParams() {
  const { data }: { data: OfferSlugsQueryResult } = await sanityFetch({
    query: offerSlugsQuery,
    perspective: "published",
    stega: false,
  });
  return data.map((offer) => ({
    slug: offer.params.slug,
  }));
}

export async function generateMetadata({
  params,
}: OfferPageProps): Promise<Metadata> {
  const { slug } = await params;

  const { data: offer } = await sanityFetch({
    query: offerBySlugQuery,
    params: { slug: slug },
    stega: false,
  });

  if (!offer) {
    return {};
  }

  return processMetadata({ data: offer });
}

export default async function OfferPage({ params }: OfferPageProps) {
  const { slug } = await params;

  const { data: offer }: { data: OfferBySlugQueryResult } = await sanityFetch({
    query: offerBySlugQuery,
    params: { slug: slug },
  });

  if (!offer) {
    notFound();
  }

  // Get brand colors for CSS custom properties
  const brandColor = getBrandColor(offer.brand?.brandColors?.primaryColor);
  const brandColorStyles = {
    "--brand-color-primary": brandColor || "#333",
    "--brand-color-secondary":
      getBrandColor(offer.brand?.brandColors?.secondaryColor) || "#666",
    "--brand-color-accent":
      getBrandColor(offer.brand?.brandColors?.accentColor) || "#999",
  };

  return (
    <>
      <div style={brandColorStyles as React.CSSProperties}>
        <div className="relative grid grid-cols-12 gap-y-10 xl:gap-20 max-w-10xl mx-auto pt-8 lg:pt-0">
          {/* Left Sidebar - Navigation */}
          <aside className="col-span-12 xl:col-span-2 xl:sticky xl:top-28 h-fit order-0 xl:order-0 -translate-x-1 md:-translate-x-0">
            <OfferNavigation offer={offer} />
          </aside>

          {/* Main Content - Hero & Details */}
          <main className="pt-8 xl:pt-0 col-span-12 xl:col-span-7 order-1 xl:order-1 xl:pl-10 xl:border-l xl:border-dashed">
            <div className="border border-dashed rounded-3xl p-6 mb-8 bg-card/30">
              <OfferHero offer={offer} />
            </div>

            <div className="border border-dashed rounded-3xl p-6">
              <OfferContent offer={offer} />
            </div>
          </main>

          {/* Right Sidebar - Quick Actions */}
          <aside className="hidden xl:block order-2 xl:order-2 col-span-12 xl:col-span-3 xl:sticky xl:top-28 h-fit space-y-5">
            <OfferQuickActions offer={offer} />
          </aside>

          {/* Mobile Quick Actions */}
          <div className="block xl:hidden fixed bottom-0 left-0 right-0 w-full z-50">
            <Sheet>
              <SheetTrigger asChild>
                <Button
                  variant="secondary"
                  className="brand w-full h-14 rounded-none"
                >
                  Claim Offer
                </Button>
              </SheetTrigger>
              <SheetContent side="bottom" className="p-2">
                <SheetHeader>
                  <SheetTitle>Ready to Claim This Offer?</SheetTitle>
                </SheetHeader>
                <OfferQuickActions offer={offer} defaultActionOpen={false} />
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </>
  );
}
