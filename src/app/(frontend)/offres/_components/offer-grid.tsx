"use client";

import { useState, useEffect } from "react";
import OfferCard from "@/components/offers/offer-card";
import { extractBrandsFromOffers } from "./offers-filters";
import type { OfferFilters } from "./offers-filters";
import type {
  AllOffersQueryResult,
  OfferBySlugQueryResult,
} from "../../../../../sanity.types";
import OffersFilters from "./offers-filters";
import { Button } from "@/components/ui/button";
import { Filter } from "lucide-react";
import {
  Sheet,
  <PERSON><PERSON><PERSON>ontent,
  Sheet<PERSON>eader,
  Sheet<PERSON><PERSON>le,
  SheetTrigger,
} from "@/components/ui/sheet";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";

type Offer = NonNullable<AllOffersQueryResult>[number];

interface OfferGridProps {
  offers: Offer[];
}

export default function OfferGrid({ offers }: OfferGridProps) {
  const [filteredOffers, setFilteredOffers] = useState<Offer[]>(offers);
  const [filters, setFilters] = useState<OfferFilters>({});
  const brands = extractBrandsFromOffers(offers);

  // Apply filters when they change
  useEffect(() => {
    let result = [...offers];

    if (filters.offerType) {
      result = result.filter((offer) => offer.offerType === filters.offerType);
    }

    if (filters.brandSlug) {
      result = result.filter(
        (offer) => offer.brand?.slug === filters.brandSlug,
      );
    }

    if (filters.isLimitedTime) {
      result = result.filter(
        (offer) => offer.validityPeriod?.isLimitedTime === true,
      );
    }

    if (filters.isFeatured) {
      result = result.filter(
        (offer) => offer.displaySettings?.isFeatured === true,
      );
    }

    if (filters.hasFinancing) {
      result = result.filter(
        (offer) =>
          offer.financingDetails !== null &&
          offer.financingDetails !== undefined,
      );
    }

    setFilteredOffers(result);
  }, [filters, offers]);

  if (!offers || offers.length === 0) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-semibold ">No offers found</h2>
        <p className="mt-2 ">Check back later for new offers.</p>
      </div>
    );
  }

  return (
    <div className="relative">
      <div className="grid grid-cols-12 gap-8">
        {/* Filters Sidebar - Desktop */}
        <div className="hidden lg:block col-span-12 lg:col-span-3 sticky top-24">
          <OffersFilters
            brands={brands}
            initialFilters={filters}
            onFiltersChange={setFilters}
          />
        </div>

        {/* Offers Grid */}
        <div className="col-span-12 lg:col-span-9">
          {/* filters applied badges */}
          <div className="flex flex-wrap gap-2 mb-4">
            {filters.offerType && (
              <div className="col-span-12 lg:col-span-9">
                <Badge>Offer Type: {filters.offerType}</Badge>
              </div>
            )}
            {filters.brandSlug && (
              <div className="col-span-12 lg:col-span-9">
                <Badge>Brand: {filters.brandSlug}</Badge>
              </div>
            )}
            {filters.isLimitedTime && (
              <div className="col-span-12 lg:col-span-9">
                <Badge>Limited Time</Badge>
              </div>
            )}
            {filters.isFeatured && (
              <div className="col-span-12 lg:col-span-9">
                <Badge>Featured</Badge>
              </div>
            )}
            {filters.hasFinancing && (
              <div className="col-span-12 lg:col-span-9">
                <Badge>Financing Available</Badge>
              </div>
            )}
          </div>

          {filteredOffers.length > 0 ? (
            <div className="grid grid-cols-12 gap-6">
              {filteredOffers.map((offer, index) => (
                <OfferCard
                  key={offer._id}
                  offer={offer as NonNullable<OfferBySlugQueryResult>}
                  priority={index < 6 ? "high" : "normal"}
                  variant={
                    offer.displaySettings?.isFeatured ? "featured" : "grid"
                  }
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-12 bg-accent rounded-lg">
              <h3 className="text-xl font-semibold ">
                No offers match your filters
              </h3>
              <p className="mt-2 ">
                Try adjusting your filters or browse all offers.
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Mobile Filter Button */}
      <div className="block lg:hidden fixed bottom-0 left-0 right-0 w-full z-50">
        <Sheet>
          <SheetTrigger asChild className="">
            <Button variant="secondary" className="w-full h-14 rounded-none">
              <Filter className="h-4 w-4 mr-2" />
              Filtres{" "}
              {
                // count filters
                Object.values(filters).filter(Boolean).length > 0 && (
                  <Badge variant="secondary">
                    {Object.values(filters).filter(Boolean).length}
                  </Badge>
                )
              }
            </Button>
          </SheetTrigger>
          <SheetContent side="bottom" className="h-[80vh]">
            <SheetHeader className="flex items-center justify-between mb-6">
              <SheetTitle className="text-lg font-semibold">Filtres</SheetTitle>
            </SheetHeader>
            <ScrollArea className="h-full pb-8 p-4">
              <OffersFilters
                brands={brands}
                initialFilters={filters}
                onFiltersChange={setFilters}
              />
            </ScrollArea>
          </SheetContent>
        </Sheet>
      </div>
    </div>
  );
}
