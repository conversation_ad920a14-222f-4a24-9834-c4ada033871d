"use client";

import Link from "next/link";
import { ChevronLeft, ChevronDown, Car } from "lucide-react";
import { Button } from "@/components/ui/button";
import { getBrandColor } from "@/components/brands/models/utils";
import AnimatedUnderline from "@/components/shared/animated-underline";
import { cn } from "@/lib/utils";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { useState } from "react";
import type { OfferBySlugQueryResult } from "../../../../../sanity.types";
import Image from "next/image";

type Offer = NonNullable<OfferBySlugQueryResult>;

interface OfferNavigationProps {
  offer: Offer;
}

export default function OfferNavigation({ offer }: OfferNavigationProps) {
  const [isRelatedOffersOpen, setIsRelatedOffersOpen] = useState(true);

  const brandColor = getBrandColor(offer.brand?.brandColors?.primaryColor);

  // Get applicable models for this offer
  const featuredModels = offer.applicableModels?.slice(0, 3) || [];

  return (
    <div className="space-y-5">
      <Button
        variant="outline"
        size="sm"
        className="w-full justify-start"
        asChild
      >
        <Link href="/offres">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Back to Offers
        </Link>
      </Button>

      {/* Featured Models */}
      {featuredModels.length > 0 && (
        <Collapsible
          open={isRelatedOffersOpen}
          onOpenChange={setIsRelatedOffersOpen}
        >
          <CollapsibleTrigger className="w-full">
            <div className="py-1.5 pl-2 flex items-center justify-between border border-dashed rounded-lg">
              <div className="flex items-center gap-2">
                <span className="h-5 w-5 flex items-center justify-center rounded-sm bg-card">
                  <Car size={12} />
                </span>
                <span className="font-medium text-sm">Applicable Models</span>
              </div>
              <ChevronDown
                size={15}
                className={cn(
                  "mr-2.5 -rotate-90 transition-transform duration-200",
                  {
                    "-rotate-0": isRelatedOffersOpen,
                  },
                )}
              />
            </div>
          </CollapsibleTrigger>
          <CollapsibleContent className="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 transition-all duration-200">
            <div className="mt-4 space-y-2 border-l border-dashed">
              {featuredModels.map((model) => (
                <div key={model._id}>
                  <Link
                    href={`/gammes/${model.brand?.slug}/modeles/${model.slug}`}
                    className="flex items-center gap-2 scroll-smooth focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    <span className="block w-2.5 border-t border-dashed text-muted-foreground" />
                    <div className="relative group w-fit">
                      <div className="text-sm font-medium">{model.name}</div>
                      <div className="text-xs text-muted-foreground">
                        {model.category} • {model.modelYear || ""}
                      </div>
                      <AnimatedUnderline
                        style={{ backgroundColor: brandColor }}
                      />
                    </div>
                  </Link>
                </div>
              ))}

              {/* View All Models Link */}
              {offer.brand && (
                <div className="pt-2">
                  <Link
                    href={`/gammes/${offer.brand?.slug}/modeles`}
                    className="flex items-center gap-2 scroll-smooth focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    <span className="block w-2.5 border-t border-dashed text-muted-foreground" />
                    <div className="relative group w-fit">
                      <div className="text-sm font-medium">View All Models</div>
                      <div className="text-xs text-muted-foreground">
                        Complete {offer.brand?.name} range
                      </div>
                      <AnimatedUnderline
                        style={{ backgroundColor: brandColor }}
                      />
                    </div>
                  </Link>
                </div>
              )}
            </div>
          </CollapsibleContent>
        </Collapsible>
      )}

      {/* Brand Information */}
      {offer.brand && (
        <div className="py-4 px-3 border border-dashed rounded-lg bg-card/30">
          <div className="flex items-center gap-3 mb-3">
            {/* @ts-expect-error: weak reference */}
            {offer.brand.logo?.asset?.url && (
              <Image
                // @ts-expect-error: weak reference
                src={offer.brand.logo.asset.url}
                alt={offer.brand.name as string}
                width={40}
                height={40}
                className="w-10 h-10 object-contain"
              />
            )}
            <div className="text-sm font-medium">{offer.brand.name}</div>
          </div>
          <div className="text-xs text-muted-foreground leading-relaxed">
            Explore more special offers and vehicles from {offer.brand.name}.
          </div>
          <div className="mt-3">
            <Link
              href={`/gammes/${offer.brand?.slug}`}
              className="inline-flex items-center gap-1 text-xs font-medium hover:underline"
              style={{ color: brandColor }}
            >
              Visit Brand Page
              <ChevronDown size={12} className="rotate-[-90deg]" />
            </Link>
          </div>
        </div>
      )}

      {/* Related Offers */}
      {offer.relatedOffers && offer.relatedOffers.length > 0 && (
        <div className="py-4 px-3 border border-dashed rounded-lg bg-card/30">
          <div className="text-sm font-medium mb-3">Related Offers</div>
          <div className="space-y-3">
            {offer.relatedOffers.slice(0, 3).map((relatedOffer) => (
              <Link
                key={relatedOffer._id}
                href={`/offres/${relatedOffer.slug}`}
                className="block group"
              >
                <div
                  className="text-sm font-medium group-hover:text-[var(--brand-color-primary)]"
                  style={
                    {
                      "--brand-color-primary": brandColor,
                    } as React.CSSProperties
                  }
                >
                  {relatedOffer.title}
                </div>
                {relatedOffer.shortDescription && (
                  <p className="text-xs text-muted-foreground mt-0.5 line-clamp-1">
                    {relatedOffer.shortDescription}
                  </p>
                )}
              </Link>
            ))}

            {offer.relatedOffers.length > 3 && (
              <Link
                href="/offres"
                className="inline-flex items-center gap-1 text-xs font-medium hover:underline mt-2"
                style={{ color: brandColor }}
              >
                View All Offers
                <ChevronDown size={12} className="rotate-[-90deg]" />
              </Link>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
