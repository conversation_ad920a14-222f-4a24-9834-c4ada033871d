"use client";

import { useState, useCallback, useMemo } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import type { AllOffersQueryResult } from "../../../../../sanity.types";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

export interface OfferFilters {
  offerType?: string | null;
  brandSlug?: string | null;
  isLimitedTime?: boolean | null;
  isFeatured?: boolean | null;
  hasFinancing?: boolean | null;
}

interface BrandOption {
  name: string;
  slug: string;
}

interface OffersFiltersProps {
  brands: BrandOption[];
  initialFilters?: OfferFilters;
  onFiltersChange: (filters: OfferFilters) => void;
}

export function extractBrandsFromOffers(
  offers: NonNullable<AllOffersQueryResult>,
): BrandOption[] {
  const brandsMap = new Map<string, BrandOption>();

  offers.forEach((offer) => {
    if (offer.brand && offer.brand.slug && !brandsMap.has(offer.brand.slug)) {
      brandsMap.set(offer.brand.slug, {
        name: offer.brand.name || "",
        slug: offer.brand.slug,
      });
    }
  });

  return Array.from(brandsMap.values()).sort((a, b) =>
    a.name.localeCompare(b.name),
  );
}

export default function OffersFilters({
  brands,
  initialFilters = {},
  onFiltersChange,
}: OffersFiltersProps) {
  const [filters, setFilters] = useState<OfferFilters>(initialFilters);

  const offerTypes = useMemo(
    () => [
      { value: "financing", label: "Financing" },
      { value: "leasing", label: "Leasing" },
      { value: "discount", label: "Discount" },
      { value: "rebate", label: "Rebate" },
      { value: "trade_in", label: "Trade-in" },
      { value: "promotional", label: "Promotional" },
      { value: "seasonal", label: "Seasonal" },
      { value: "clearance", label: "Clearance" },
    ],
    [],
  );

  const handleFilterChange = useCallback(
    (key: keyof OfferFilters, value: string | boolean | null) => {
      const newFilters = { ...filters, [key]: value };
      setFilters(newFilters);
      onFiltersChange(newFilters);
    },
    [filters, onFiltersChange],
  );

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            Filters{" "}
            {
              // count filters
              Object.values(filters).filter(Boolean).length > 0 && (
                <Badge variant="secondary">
                  {Object.values(filters).filter(Boolean).length}
                </Badge>
              )
            }
          </CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              setFilters({});
              onFiltersChange({});
            }}
          >
            Clear All
          </Button>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Offer Type Filter */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">Offer Type</Label>
          <Badge
            className={cn(filters.offerType ? "" : "hidden")}
            onClick={() => handleFilterChange("offerType", null)}
          >
            Reset filter
          </Badge>

          <Select
            key={filters.offerType || "empty-offer-type"}
            value={filters.offerType ?? undefined}
            onValueChange={(value) =>
              handleFilterChange("offerType", value || null)
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Séléctionnez un type d'offre" />
            </SelectTrigger>
            <SelectContent>
              {offerTypes.map((type) => (
                <SelectItem key={type.value} value={type.value}>
                  {type.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <Separator />

        {/* Brand Filter */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">Brand</Label>
          <Badge
            className={cn(filters.brandSlug ? "" : "hidden")}
            onClick={() => handleFilterChange("brandSlug", null)}
          >
            Reset filter
          </Badge>
          <Select
            key={filters.brandSlug || "empty-brand-slug"}
            value={filters.brandSlug ?? undefined}
            onValueChange={(value) =>
              handleFilterChange("brandSlug", value || null)
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Sélectionnez une marque" />
            </SelectTrigger>
            <SelectContent>
              {brands.map((brand) => (
                <SelectItem key={brand.slug} value={brand.slug}>
                  {brand.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <Separator />

        {/* Feature Filters */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Features</Label>
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="limited-time"
                checked={filters.isLimitedTime === true}
                onCheckedChange={(checked) =>
                  handleFilterChange("isLimitedTime", checked ? true : null)
                }
              />
              <Label htmlFor="limited-time" className="text-sm font-normal">
                Limited Time Only
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="featured"
                checked={filters.isFeatured === true}
                onCheckedChange={(checked) =>
                  handleFilterChange("isFeatured", checked ? true : null)
                }
              />
              <Label htmlFor="featured" className="text-sm font-normal">
                Featured Offers
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="has-financing"
                checked={filters.hasFinancing === true}
                onCheckedChange={(checked) =>
                  handleFilterChange("hasFinancing", checked ? true : null)
                }
              />
              <Label htmlFor="has-financing" className="text-sm font-normal">
                Financing Available
              </Label>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
