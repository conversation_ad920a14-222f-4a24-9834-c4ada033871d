"use client";

import Link from "next/link";
import {
  Phone,
  ExternalLink,
  Calendar,
  MapPin,
  ChevronDown,
} from "lucide-react";
import { getBrandColor } from "@/components/brands/models/utils";
import AnimatedUnderline from "@/components/shared/animated-underline";
import { cn } from "@/lib/utils";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import React, { useState } from "react";
import type { OfferBySlugQueryResult } from "../../../../../sanity.types";
import { Button } from "@/components/ui/button";

type Offer = NonNullable<OfferBySlugQueryResult>;

interface OfferQuickActionsProps {
  offer: Offer;
  defaultActionOpen?: boolean;
}

export default function OfferQuickActions({
  offer,
  defaultActionOpen = true,
}: OfferQuickActionsProps) {
  const [isQuickActionsOpen, setIsQuickActionsOpen] =
    useState(defaultActionOpen);

  const brandColor = getBrandColor(offer.brand?.brandColors?.primaryColor);

  // Define quick actions based on offer data
  const quickActions = [
    {
      icon: Phone,
      label: "Contact Sales",
      href: offer.contactInformation?.contactPhone
        ? `tel:${offer.contactInformation.contactPhone}`
        : "#contact",
      description: "Speak with specialist",
      condition: true,
    },
    {
      icon: ExternalLink,
      label: "Apply Online",
      href: offer.onlineApplication?.applicationUrl || "#contact",
      description: "Start application",
      condition:
        offer.onlineApplication?.isEnabled &&
        offer.onlineApplication.applicationUrl,
    },
    {
      icon: Calendar,
      label: "Schedule Test Drive",
      href: `/gammes/${offer.brand?.slug}/test-drive`,
      description: "Experience the vehicle",
      condition: offer.applicableModels && offer.applicableModels.length > 0,
    },
    {
      icon: MapPin,
      label: "Find Dealer",
      href: `/gammes/${offer.brand?.slug}/dealers`,
      description: "Locate nearest dealer",
      condition: offer.brand,
    },
  ];

  const visibleQuickActions = quickActions.filter((action) => action.condition);

  return (
    <div className="space-y-5">
      <div className="py-4 px-3 border border-dashed rounded-lg">
        <h3 className="hidden lg:block text-lg font-semibold mb-4">
          Ready to Claim This Offer?
        </h3>

        {offer?.offerCode && (
          <div
            className="p-4 border border-dashed rounded-lg"
            style={{ borderColor: `${brandColor}40` }}
          >
            <div className="text-center">
              <div className="text-sm text-muted-foreground mb-1">
                Votre code promotionnel
              </div>
              <div
                className="text-2xl font-bold mb-2"
                style={{ color: brandColor }}
              >
                {offer.offerCode}
              </div>
            </div>
          </div>
        )}

        {/* Offer Details Summary */}
        <div className="p-4">
          <div className="text-sm font-medium">{offer.title}</div>
          {offer.shortDescription && (
            <p className="text-sm text-muted-foreground mt-1">
              {offer.shortDescription}
            </p>
          )}

          {/* Validity Period */}
          {offer.validityPeriod?.validTo && (
            <div className="mt-3 text-xs text-muted-foreground">
              <span className="font-medium">Valid until: </span>
              {new Date(offer.validityPeriod.validTo).toLocaleDateString(
                "fr-CH",
              )}
            </div>
          )}
        </div>

        {/* Quick Actions */}
        <Collapsible
          open={isQuickActionsOpen}
          onOpenChange={setIsQuickActionsOpen}
        >
          <CollapsibleTrigger className="w-full">
            <div className="py-1.5 pl-2 flex items-center justify-between border border-dashed rounded-lg">
              <div className="flex items-center gap-2">
                <span className="h-5 w-5 flex items-center justify-center rounded-sm bg-card">
                  <Phone size={12} />
                </span>
                <span className="font-medium text-sm">Quick Actions</span>
              </div>
              <ChevronDown
                size={15}
                className={cn(
                  "mr-2.5 -rotate-90 transition-transform duration-200",
                  {
                    "-rotate-0": isQuickActionsOpen,
                  },
                )}
              />
            </div>
          </CollapsibleTrigger>
          <CollapsibleContent className="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 transition-all duration-200">
            <div className="mt-4 space-y-2 border-l border-dashed">
              {visibleQuickActions.map((action) => (
                <div key={action.href}>
                  <Link
                    href={action.href}
                    className="flex items-center gap-2 scroll-smooth focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    <span className="block w-2.5 border-t border-dashed text-muted-foreground" />
                    <div className="flex items-center gap-2">
                      <action.icon
                        size={14}
                        className="text-muted-foreground"
                      />
                      <div className="relative group w-fit">
                        <div className="text-sm font-medium">
                          {action.label}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {action.description}
                        </div>
                        <AnimatedUnderline
                          style={{ backgroundColor: brandColor }}
                        />
                      </div>
                    </div>
                  </Link>
                </div>
              ))}
            </div>
          </CollapsibleContent>
        </Collapsible>
      </div>
      {/* Vehicle Contact Info */}
      <div className="py-4 px-3 border border-dashed rounded-lg bg-card/30">
        <div
          className="flex gap-2 w-full justify-end"
          style={{ "--brand-color-primary": brandColor }}
        >
          <Link href={`/contact`}>
            <Button variant="primary" className="brand w-full">
              Test Drive
            </Button>
          </Link>
          <Link href={`/contact`}>
            <Button variant="outline-solid" className="w-full">
              Contact
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
}
