import { Suspense } from 'react';
import { BrandGridLoader } from '@/components/global/navigation/brand-grid-loader';
import { BrandGridSkeleton } from '@/components/global/navigation/brand-grid-skeleton';

/**
 * Test page to verify brand navigation components work correctly
 * This can be removed after testing
 */
export default function TestNavigationPage() {
  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6">Brand Navigation Test</h1>
      
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Brand Grid (with data)</h2>
        <Suspense fallback={<BrandGridSkeleton />}>
          <BrandGridLoader />
        </Suspense>
      </div>
      
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Loading Skeleton</h2>
        <BrandGridSkeleton />
      </div>
    </div>
  );
}
