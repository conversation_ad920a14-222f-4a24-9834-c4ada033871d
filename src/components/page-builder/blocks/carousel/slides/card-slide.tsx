"use client";

import React from "react";
import Image from "next/image";
import { ArrowRight, Image as ImageIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import PortableTextEditor from "@/components/portable-text/portable-text-editor";
import type { SlideProps } from "../types";
import Link from "next/link";

/**
 * CardSlide Component
 *
 * Renders a card slide with image, title, description, and optional button
 * Shows a placeholder if no image is available
 */
export const CardSlide: React.FC<SlideProps> = ({ slide }) => (
  <div className="border border-dashed rounded-3xl overflow-hidden h-full flex flex-col group">
    {slide.image?.asset?.url ? (
      <div className="aspect-video relative overflow-hidden">
        <Image
          src={slide.image.asset.url}
          alt={slide.image.alt || slide.title || ""}
          fill
          className="object-cover group-hover:scale-105 transition-transform duration-500"
        />
      </div>
    ) : (
      <div className="aspect-video relative overflow-hidden bg-card flex items-center justify-center">
        <div className="text-center">
          <ImageIcon className="w-12 h-12 text-gray-400 mx-auto mb-2" />
          <p className="text-sm text-muted-foreground font-medium">
            {slide.title || "Add Image"}
          </p>
        </div>
      </div>
    )}

    <div className="p-6 space-y-4 flex flex-col flex-1">
      {slide.title && (
        <h3 className="text-lg font-bold text-card-foreground leading-tight">
          {slide.title}
        </h3>
      )}

      {slide.description && (
        <p className="text-muted-foreground flex-1 leading-relaxed text-sm">
          {slide.description}
        </p>
      )}

      {slide.content && (
        <div className="flex-1">
          <PortableTextEditor
            data={slide.content}
            classNames="text-sm text-muted-foreground"
          />
        </div>
      )}

      {slide.button && (
        <div className="pt-2 mt-auto border-t border-dashed lg:ml-auto max-w-md">
          <Link href={slide.button.url ?? "#"}>
            <Button
              variant={slide.button.style ?? "primary"}
              className="h-12 w-full"
            >
              {slide.button.text}
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </Link>
        </div>
      )}
    </div>
  </div>
);
