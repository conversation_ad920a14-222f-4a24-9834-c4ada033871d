import { cn } from "@/lib/utils";
import Heading from "@/components/shared/heading";
import PortableTextEditor from "@/components/portable-text/portable-text-editor";
import Image from "next/image";
import { motion } from "motion/react";
import {
  Calendar,
  ExternalLink,
  Filter,
  ImageIcon,
  Search,
  Tag,
  User,
} from "lucide-react";
import { useEffect, useMemo, useState } from "react";
import {
  type DataSourceConfig,
  fetchDataSource,
  type GridItem,
} from "@/lib/data-source-fetcher";
import { stegaClean } from "next-sanity";
import Link from "next/link";

export type ContentGridsBlockProps = {
  _key: string;
  _type: string;
  title?: string;
  subtitle?: string;
  dataSource?: DataSourceConfig;
  manualItems?: GridItem[];
  gridConfig?: {
    columns?: {
      mobile: number;
      tablet: number;
      desktop: number;
    };
    spacing?: string;
    alignment?: string;
    aspectRatio?: string;
  };
  filterConfig?: {
    enableFilters?: boolean;
    enableSearch?: boolean;
    enableCategoryFilter?: boolean;
    enableTagFilter?: boolean;
    enableDateFilter?: boolean;
    enableSorting?: boolean;
    sortOptions?: string[];
    availableFilters?: string[];
    defaultFilter?: string;
  };
  enableLoadMore?: boolean;
  initialItemCount?: number;
  backgroundColor?: {
    value: string;
  };
  imageFit?: string;
  anchorId?: string;
};

interface GridCardProps {
  item: GridItem;
  imageFit?: string;
  index: number;
}

function GridCard({ item, imageFit, index }: GridCardProps) {
  const displayTitle = item.title || item.name;
  const displayDescription =
    item.shortDescription || item.description || item.quote || item.bio;
  const displayImage = item.image || item.avatar;
  const displayCategories = Array.isArray(item.categories)
    ? item.categories
    : item.category
      ? [
          typeof item.category === "string"
            ? item.category
            : item.category.title,
        ]
      : [];

  return (
    <motion.div
      key={(item._key || item._id || `item`) + `-${index}`}
      className="border border-dashed rounded-3xl overflow-hidden h-full flex flex-col group"
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, margin: "-10%" }}
      transition={{
        duration: 0.6,
        delay: index * 0.1,
        ease: [0.25, 0.1, 0.25, 1],
      }}
      whileHover={{
        y: -4,
        transition: { duration: 0.3, ease: [0.25, 0.1, 0.25, 1] },
      }}
    >
      {displayImage?.asset?.url ? (
        <div className="aspect-4/3 relative overflow-hidden bg-muted-foreground dark:bg-muted">
          <Image
            src={displayImage.asset.url}
            alt={displayImage.alt || displayImage.altText || displayTitle || ""}
            fill
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            className={cn(
              "group-hover:scale-105 transition-transform duration-500",
              {
                "object-contain": stegaClean(imageFit) === "contain",
                "object-cover": stegaClean(imageFit) === "cover",
              },
            )}
          />
          {item.featured && (
            <div className="absolute top-4 left-4 bg-primary text-foreground px-3 py-1.5 rounded-full text-xs font-semibold">
              Featured
            </div>
          )}
        </div>
      ) : (
        <div className="aspect-4/3 relative overflow-hidden bg-muted-foreground dark:bg-muted">
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <ImageIcon className="w-12 h-12 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-muted-foreground font-medium">
                {displayTitle || "Add Image"}
              </p>
            </div>
          </div>
        </div>
      )}

      <div className="p-6 space-y-4 flex flex-col flex-1">
        {displayTitle && (
          <h3 className="text-lg font-bold text-card-foreground leading-tight line-clamp-2">
            {displayTitle}
          </h3>
        )}

        {/* Author/Company info for testimonials and authors */}
        {(item.jobTitle || item.company || item.username) && (
          <div className="flex items-center gap-2 text-sm text-accent-foreground">
            <User className="w-4 h-4" />
            <span>
              {item.jobTitle && item.company
                ? `${item.jobTitle} at ${item.company}`
                : item.jobTitle || item.company || `@${item.username}`}
            </span>
          </div>
        )}

        {/* Author info for posts */}
        {item.author && typeof item.author === "object" && (
          <div className="flex items-center gap-2 text-sm text-accent-foreground">
            <User className="w-4 h-4" />
            <span>By {item.author.name}</span>
          </div>
        )}

        {/* Published date */}
        {item.publishedAt && (
          <div className="flex items-center gap-2 text-sm text-accent-foreground">
            <Calendar className="w-4 h-4" />
            <span>
              {new Date(item.publishedAt).toLocaleDateString("fr-CH")}
            </span>
          </div>
        )}

        {displayDescription &&
        typeof displayDescription === "object" &&
        displayDescription._type === "richTextObject" ? (
          <PortableTextEditor
            data={displayDescription.richTextContent}
            classNames="text-sm text-muted-foreground"
          />
        ) : (
          <p className="text-muted-foreground leading-relaxed line-clamp-3 flex-1 text-sm">
            {displayDescription as string}
          </p>
        )}

        {(displayCategories.length > 0 ||
          (item.tags && item.tags.length > 0)) && (
          <div className="flex flex-wrap gap-2 border-t border-dashed pt-4">
            {displayCategories.slice(0, 2).map((category, idx) => (
              <span
                key={`category-${idx}`}
                className="px-3 py-1 bg-primary/10 text-primary-hover rounded-full text-xs font-medium border border-dashed"
              >
                {typeof category === "string"
                  ? category
                  : category.title || category._id}
              </span>
            ))}
            {item.tags?.slice(0, 1).map((tag, idx) => (
              <span
                key={`tag-${idx}`}
                className="px-3 py-1 bg-card text-muted-foreground rounded-full text-xs flex items-center gap-1 border border-dashed"
              >
                <Tag className="w-3 h-3" />
                {tag}
              </span>
            ))}
          </div>
        )}

        {item.link && (
          <div className="pt-2 mt-auto">
            {item.link.openInNewTab ? (
              <ExternalLink
                href={item.link.url}
                className="ml-1 w-4 h-4 transform group-hover/link:translate-x-1 transition-transform duration-200"
              >
                {item.link.label || "Learn More"}
                <span className="ml-1 transform group-hover/link:translate-x-1 transition-transform duration-200">
                  →
                </span>
              </ExternalLink>
            ) : (
              <Link
                href={item.link.url ?? "#"}
                className="inline-flex items-center text-primary hover:text-primary-hover font-semibold text-sm group/link"
              >
                {item.link.label || "Learn More"}
                <span className="ml-1 transform group-hover/link:translate-x-1 transition-transform duration-200">
                  →
                </span>
              </Link>
            )}
          </div>
        )}
      </div>
    </motion.div>
  );
}

export default function ContentGridsBlock(props: ContentGridsBlockProps) {
  const {
    title,
    subtitle,
    dataSource,
    manualItems,
    gridConfig,
    filterConfig,
    enableLoadMore,
    initialItemCount,
    backgroundColor,
    imageFit,
    anchorId,
  } = props;

  const [items, setItems] = useState<GridItem[]>(manualItems || []);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("");
  const [selectedTag, setSelectedTag] = useState<string>("");
  const [sortBy, setSortBy] = useState<string>("date");
  const [showAll, setShowAll] = useState(false);

  // Fetch data on component mount
  useEffect(() => {
    async function loadData() {
      if (!dataSource) {
        setItems(manualItems || []);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        const fetchedItems = await fetchDataSource(dataSource);
        const allItems = [...fetchedItems, ...(manualItems || [])];
        setItems(allItems);
      } catch (err) {
        console.error("Failed to load data:", err);
        setError(
          dataSource?.errorHandling?.fallbackValue || "Failed to load content",
        );
        setItems(manualItems || []);
      } finally {
        setLoading(false);
      }
    }

    loadData();
  }, [dataSource, manualItems]);

  // Get available categories and tags for filtering
  const availableCategories = useMemo(() => {
    const categories = new Set<string>();
    items.forEach((item) => {
      if (Array.isArray(item.categories)) {
        item.categories.forEach((cat) => {
          if (typeof cat === "string") categories.add(cat);
          else if (cat?.title) categories.add(cat.title);
        });
      } else if (item.category) {
        if (typeof item.category === "string") categories.add(item.category);
        else if (item.category?.title) categories.add(item.category.title);
      }
    });
    return Array.from(categories).sort();
  }, [items]);

  const availableTags = useMemo(() => {
    const tags = new Set<string>();
    items.forEach((item) => {
      if (item.tags) {
        item.tags.forEach((tag) => tags.add(tag));
      }
    });
    return Array.from(tags).sort();
  }, [items]);

  // Filter and sort items
  const filteredItems = useMemo(() => {
    let filtered = [...items];

    // Search filter
    if (searchTerm && filterConfig?.enableSearch) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (item) =>
          item.title?.toLowerCase().includes(term) ||
          item.shortDescription?.toLowerCase().includes(term) ||
          item.name?.toLowerCase().includes(term) ||
          item.quote?.toLowerCase().includes(term),
      );
    }

    // Category filter
    if (selectedCategory && filterConfig?.enableCategoryFilter) {
      filtered = filtered.filter((item) => {
        if (Array.isArray(item.categories)) {
          return item.categories.some(
            (cat) =>
              (typeof cat === "string" ? cat : cat?.title) === selectedCategory,
          );
        }
        if (item.category) {
          const categoryName =
            typeof item.category === "string"
              ? item.category
              : item.category?.title;
          return categoryName === selectedCategory;
        }
        return false;
      });
    }

    // Tag filter
    if (selectedTag && filterConfig?.enableTagFilter) {
      filtered = filtered.filter((item) => item.tags?.includes(selectedTag));
    }

    // Sort
    if (filterConfig?.enableSorting) {
      filtered.sort((a, b) => {
        switch (sortBy) {
          case "title":
            return (a.title || a.name || "").localeCompare(
              b.title || b.name || "",
            );
          case "date":
            const aDate = new Date(a.publishedAt || a._createdAt || 0);
            const bDate = new Date(b.publishedAt || b._createdAt || 0);
            return bDate.getTime() - aDate.getTime();
          case "featured":
            if (a.featured && !b.featured) return -1;
            if (!a.featured && b.featured) return 1;
            return 0;
          default:
            return 0;
        }
      });
    }

    return filtered;
  }, [items, searchTerm, selectedCategory, selectedTag, sortBy, filterConfig]);

  // Items to display (with load more functionality)
  const displayedItems = useMemo(() => {
    if (!enableLoadMore || showAll) return filteredItems;
    return filteredItems.slice(0, initialItemCount || 6);
  }, [filteredItems, enableLoadMore, showAll, initialItemCount]);

  // Get grid configuration with defaults
  const columns = gridConfig?.columns || { mobile: 1, tablet: 2, desktop: 3 };
  const spacing = gridConfig?.spacing || "6";

  return (
    <section
      {...(anchorId ? { id: anchorId } : {})}
      className={cn("px-4 md:px-10 py-16 md:py-24", {
        "pattern-bg": !backgroundColor,
      })}
    >
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        {(title || subtitle) && (
          <div className="text-center max-w-3xl mx-auto mb-8">
            {title && (
              <Heading size="xl" tag="h2" className="mb-4">
                {title}
              </Heading>
            )}
            {subtitle && (
              <PortableTextEditor
                data={[
                  {
                    _type: "block",
                    children: [{ _type: "span", text: subtitle, marks: [] }],
                    style: "normal",
                  },
                ]}
                classNames="text-lg text-muted-foreground leading-relaxed"
              />
            )}
          </div>
        )}

        {/* Filters */}
        {(filterConfig?.enableSearch ||
          filterConfig?.enableCategoryFilter ||
          filterConfig?.enableTagFilter ||
          filterConfig?.enableSorting) && (
          <div className="border border-dashed rounded-3xl p-6 space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Search */}
              {filterConfig.enableSearch && (
                <div className="relative z-10">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    id="search-input"
                    type="text"
                    placeholder="Search content..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-dashed rounded-2xl focus:ring-2 focus:ring-primary/50 focus:border-transparent"
                  />
                </div>
              )}

              {/* Category Filter */}
              {filterConfig.enableCategoryFilter &&
                availableCategories.length > 0 && (
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="w-full px-4 py-2 border border-dashed rounded-2xl focus:ring-2 focus:ring-primary/50 focus:border-transparent"
                  >
                    <option className="bg-background" value="">
                      All Categories
                    </option>
                    {availableCategories.map((category) => (
                      <option
                        className="bg-background"
                        key={category}
                        value={category}
                      >
                        {category}
                      </option>
                    ))}
                  </select>
                )}

              {/* Tag Filter */}
              {filterConfig.enableTagFilter && availableTags.length > 0 && (
                <select
                  value={selectedTag}
                  onChange={(e) => setSelectedTag(e.target.value)}
                  className="w-full px-4 py-2 border border-dashed rounded-2xl focus:ring-2 focus:ring-primary/50 focus:border-transparent"
                >
                  <option value="">All Tags</option>
                  {availableTags.map((tag) => (
                    <option key={tag} value={tag}>
                      {tag}
                    </option>
                  ))}
                </select>
              )}

              {/* Sort */}
              {filterConfig.enableSorting && (
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="w-full px-4 py-2 border border-dashed rounded-2xl focus:ring-2 focus:ring-primary/50 focus:border-transparent"
                >
                  <option value="date">Latest First</option>
                  <option value="title">Alphabetical</option>
                  <option value="featured">Featured First</option>
                </select>
              )}
            </div>

            {/* Results count */}
            <div className="text-sm text-muted-foreground border-t border-dashed pt-4">
              Showing {displayedItems.length} of {filteredItems.length} items
            </div>
          </div>
        )}

        {loading && (
          <div className="flex justify-center items-center py-16 min-h-[300px]">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <motion.div
            className="bg-red-50 border border-dashed rounded-3xl p-6 text-center"
            initial={{ opacity: 0, scale: 0.95 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <div className="text-red-600 mb-2">⚠️ Content Loading Error</div>
            <p className="text-red-700">{error}</p>
          </motion.div>
        )}

        {/* Grid */}
        <div
          className={cn("grid gap-6 mx-auto", {
            "grid-cols-1": columns.mobile === 1,
            "grid-cols-2": columns.mobile === 2,
            "md:grid-cols-2": columns.tablet === 2,
            "md:grid-cols-3": columns.tablet === 3,
            "md:grid-cols-4": columns.tablet === 4,
            "lg:grid-cols-2": columns.desktop === 2,
            "lg:grid-cols-3": columns.desktop === 3,
            "lg:grid-cols-4": columns.desktop === 4,
            "gap-4": spacing === "4",
            "gap-6": spacing === "6",
            "gap-8": spacing === "8",
          })}
        >
          {displayedItems.map((item, index) => (
            <GridCard
              key={(item._key || item._id || `item`) + `-${index}`}
              item={item}
              imageFit={imageFit}
              index={index}
            />
          ))}
        </div>

        {/* Load More Button */}
        {enableLoadMore &&
          !showAll &&
          filteredItems.length > displayedItems.length && (
            <motion.div
              className="text-center"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >
              <button
                onClick={() => setShowAll(true)}
                className="inline-flex items-center px-6 py-3 bg-primary text-foreground font-semibold rounded-2xl hover:bg-primary-hover transition-colors border border-dashed"
              >
                Load More ({filteredItems.length - displayedItems.length}{" "}
                remaining)
              </button>
            </motion.div>
          )}

        {/* No Results State */}
        {displayedItems.length === 0 && !loading && !error && (
          <motion.div
            className="text-center py-16"
            initial={{ opacity: 0, scale: 0.95 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <div className="text-gray-400 mb-4">
              <Filter className="w-16 h-16 mx-auto" />
            </div>
            <h3 className="text-xl font-semibold text-card-foreground mb-2">
              No content found
            </h3>
            <p className="text-muted-foreground mb-4">
              {searchTerm || selectedCategory || selectedTag
                ? "Try adjusting your filters to see more results."
                : "Content will appear here once it's added to the grid."}
            </p>
            {(searchTerm || selectedCategory || selectedTag) && (
              <button
                onClick={() => {
                  setSearchTerm("");
                  setSelectedCategory("");
                  setSelectedTag("");
                }}
                className="text-primary hover:text-primary-hover font-medium"
              >
                Clear all filters
              </button>
            )}
          </motion.div>
        )}
      </div>
    </section>
  );
}
