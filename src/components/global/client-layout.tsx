"use client";
import Navbar from "./navbar";
import Footer from "./footer";
import localFont from "next/font/local";
import { Toaster } from "react-hot-toast";
import { usePathname } from "next/navigation";
import type {
  GeneralSettingsQueryResult,
  NavigationSettingsQueryResult,
} from "../../../sanity.types";

interface ClientLayoutProps {
  children: React.ReactNode;
  settings: GeneralSettingsQueryResult;
  navigationSettings: NavigationSettingsQueryResult;
}

const geistSans = localFont({
  src: "../../app/(frontend)/fonts/GeistVF.woff",
  variable: "--font-geist-sans",
  weight: "100 900",
});

const geistMono = localFont({
  src: "../../app/(frontend)/fonts/GeistMonoVF.woff",
  variable: "--font-geist-mono",
  weight: "100 900",
});

export default function ClientLayout({
  children,
  settings,
  navigationSettings,
}: ClientLayoutProps) {
  const pathname = usePathname();
  if (pathname.includes("/studio")) return children;

  return (
    <div
      className={`${geistSans.variable} ${geistMono.variable} font-geist-sans antialiased grid min-h-dvh grid-rows-[auto_1fr_auto]`}
    >
      {children}
      <Footer settings={settings} navigationSettings={navigationSettings} />
      <Toaster
        position="bottom-right"
        toastOptions={{
          className: "text-sm font-semibold antialiased",
          style: {
            borderRadius: "300px",
            padding: "4px 8px",
            color: "#FFFFFF",
            fontWeight: "500",
            backgroundColor: "#000000",
          },
        }}
      />
    </div>
  );
}
