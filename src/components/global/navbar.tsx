"use client";

import React from "react";
import Link from "next/link";
import Container from "./container";
import { Button } from "../ui/button";
import useScroll from "@/hooks/use-scroll";
import SiteLogo from "../shared/site-logo";
import SlideOutMenu from "./slide-out-menu";
import { usePathname } from "next/navigation";
import { cn, resolveHref } from "@/lib/utils";
import { ChevronRight, Menu } from "lucide-react";
import AnimatedText from "../shared/animated-text";
import type {
  GeneralSettingsQueryResult,
  NavigationSettingsQueryResult,
} from "../../../sanity.types";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu";
import { ModeToggle } from "@/components/shared/mode-toggle";
import type { NavigationItem } from "@/lib/navigation-utils";
import { processNavigationItems } from "@/lib/navigation-utils";
import type { PageHierarchy } from "@/lib/page-utils";
import { buildPagePath } from "@/lib/page-utils";
import type { ButtonType } from "@/types";

interface NavbarProps {
  settings: GeneralSettingsQueryResult;
  navigationSettings: NavigationSettingsQueryResult;
}
export default function Navbar({ settings, navigationSettings }: NavbarProps) {
  const pathname = usePathname();
  const hasScrolled = useScroll();

  const { navbarMenuItems } = navigationSettings?.navbar as unknown as {
    navbarMenuItems: NavigationItem[] | null;
  };
  const { showSlideOutMenu } = navigationSettings?.slideOutMenu ?? {};

  // Process navigation items to ensure proper nested URLs
  const processedNavItems = processNavigationItems(navbarMenuItems || []);
  return (
    <header
      className={cn(
        "z-60 fixed top-0 left-0 w-full py-6 border-b border-b-border-100 bg-background/80 backdrop-blur-lg transition-all duration-300 ease-in-out",
        {
          "py-4 ": hasScrolled,
        },
      )}
    >
      <Container className="flex items-center justify-between">
        <SiteLogo settings={settings} />
        <div className="flex items-center gap-3">
          <NavigationMenu viewport={false} className="hidden md:flex">
            <NavigationMenuList className="space-x-6 group/nav">
              {processedNavItems.map(
                (
                  item: NavigationItem & {
                    isButton?: boolean;
                    _key?: string;
                    title?: string;
                    showChildren?: boolean;
                  },
                ) => (
                  <React.Fragment key={item._key}>
                    {!item.isButton ? (
                      <>
                        {item.menuItemType === "group" ? (
                          <NavigationMenuItem>
                            <NavigationMenuTrigger className="group-hover/nav:opacity-40 hover:opacity-100! bg-transparent">
                              {item.title}
                            </NavigationMenuTrigger>
                            <NavigationMenuContent className="min-w-48">
                              {item.pageReferences?.map((page) => {
                                const pageUrl = resolveHref(
                                  page._type,
                                  (page.slug as unknown as string) ?? "",
                                );
                                return (
                                  <Link
                                    key={page._id}
                                    href={pageUrl ?? "/"}
                                    className="group py-1 pl-3 pr-2 flex items-center justify-between gap-6 rounded-md border border-dashed hover:bg-card"
                                  >
                                    {page.title || page.name}
                                    <ChevronRight
                                      size={14}
                                      className="text-muted-foreground group-hover:-translate-x-0.5 group-hover:text-accent-foreground transition-all duration-300"
                                    />
                                  </Link>
                                );
                              })}
                            </NavigationMenuContent>
                          </NavigationMenuItem>
                        ) : (
                          <NavigationMenuItem>
                            {/* Handle single menu item with potential children */}
                            {item.showChildren &&
                            item.pageReference?.children &&
                            item.pageReference.children.length > 0 ? (
                              <>
                                <NavigationMenuTrigger className="group-hover/nav:opacity-40 hover:opacity-100!">
                                  {item.pageReference.title}
                                </NavigationMenuTrigger>
                                <NavigationMenuContent>
                                  {/* Child pages as dropdown items */}
                                  {item.pageReference.children.map(
                                    (pageChild: unknown) => {
                                      const child: PageHierarchy =
                                        pageChild as unknown as PageHierarchy;
                                      const childUrl = buildPagePath(child);
                                      return (
                                        <Link
                                          key={child._id}
                                          href={childUrl}
                                          className="group py-1 pl-3 pr-2 flex items-center justify-between gap-6 rounded-md border border-dashed hover:bg-card"
                                        >
                                          {child.title}
                                          <ChevronRight
                                            size={14}
                                            className="text-muted-foreground group-hover:-translate-x-0.5 group-hover:text-accent-foreground transition-all duration-300"
                                          />
                                        </Link>
                                      );
                                    },
                                  )}
                                </NavigationMenuContent>
                              </>
                            ) : item.showChildren ? (
                              <>
                                <NavigationMenuTrigger className="group-hover/nav:opacity-40 hover:opacity-100!">
                                  {item.pageReference?.title || item.title}
                                </NavigationMenuTrigger>
                                <NavigationMenuContent>
                                  <div className="py-1 pl-3 pr-2 text-sm text-muted-foreground italic">
                                    No child pages yet
                                  </div>
                                </NavigationMenuContent>
                              </>
                            ) : (
                              /* Regular single menu item */
                              <Link
                                href={
                                  resolveHref(
                                    item?.pageReference?._type ?? "",
                                    (item?.pageReference
                                      ?.slug as unknown as string) ?? "",
                                  ) ?? "/"
                                }
                                className={cn(
                                  "relative overflow-hidden inline-flex transition-opacity duration-200 group-hover/nav:opacity-40 hover:opacity-100!",
                                  {
                                    "hover:underline underline-offset-38":
                                      !item.isButton,
                                    "py-2 px-4 rounded-full text-foreground bg-primary":
                                      item.isButton,
                                    "text-primary-hover": pathname.includes(
                                      `/${item.pageReference?.slug ?? ""}`,
                                    ),
                                  },
                                )}
                              >
                                <AnimatedText>
                                  {item.pageReference?.title || item.title}
                                </AnimatedText>
                              </Link>
                            )}
                          </NavigationMenuItem>
                        )}
                      </>
                    ) : (
                      <NavigationMenuItem>
                        <Button
                          variant="primary"
                          disableIcon={true}
                          buttonType="internal"
                          pageReference={
                            item.pageReference as unknown as ButtonType["buttonPageReference"]
                          }
                        >
                          {item.title}
                        </Button>
                      </NavigationMenuItem>
                    )}
                  </React.Fragment>
                ),
              )}
              {process.env.NODE_ENV === "development" && <ModeToggle />}
            </NavigationMenuList>
          </NavigationMenu>
          <div className="block md:hidden">
            {process.env.NODE_ENV === "development" && <ModeToggle />}
          </div>
          {showSlideOutMenu && (
            <SlideOutMenu
              settings={settings}
              navigationSettings={navigationSettings}
            >
              <button className="md:hidden p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-md transition-colors">
                <Menu className="h-6 w-6" />
              </button>
            </SlideOutMenu>
          )}
        </div>
      </Container>
    </header>
  );
}
