import { sanityFetch } from '@/sanity/lib/live';
import { brandNavigationGridQuery } from '@/sanity/lib/queries/fragments/navigation';
import { BrandPreviewCard } from './brand-preview-card';

/**
 * Server component that fetches and renders brand grid data
 * Data fetching happens at the leaf level following Next.js 15 best practices
 */
export async function BrandGridLoader() {
  // Data fetching happens here at the leaf using centralized query
  const { data: brands } = await sanityFetch({ 
    query: brandNavigationGridQuery,
    revalidate: 3600 // 1 hour cache
  });

  if (!brands?.length) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        No brands available
      </div>
    );
  }

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
      {brands.map((brand) => (
        <BrandPreviewCard 
          key={brand._id} 
          brand={brand}
        />
      ))}
    </div>
  );
}
