import { ReactNode } from 'react';

interface BrandNavigationContentProps {
  children: ReactNode;
}

/**
 * Server component that provides the layout shell for brand navigation content
 */
export function BrandNavigationContent({ children }: BrandNavigationContentProps) {
  return (
    <div className="py-8">
      <div className="container mx-auto">
        <h3 className="text-xl font-semibold mb-6"><PERSON>rowse by Brand</h3>
        {children}
      </div>
    </div>
  );
}
