import { Suspense } from 'react';
import { BrandNavigationTrigger } from './brand-navigation-trigger';
import { BrandNavigationContent } from './brand-navigation-content';
import { BrandGridLoader } from './brand-grid-loader';
import { BrandGridSkeleton } from './brand-grid-skeleton';
import type { NavigationLayoutType } from '@/lib/navigation-layout';

interface NavigationLayoutProps {
  title: string;
  layoutType: NavigationLayoutType;
}

/**
 * Server component that orchestrates navigation layouts
 * Passes server components as children to client components
 */
export function NavigationLayout({ title, layoutType }: NavigationLayoutProps) {
  if (layoutType === 'brand') {
    // Server component orchestrates everything and passes children to client component
    const brandContent = (
      <BrandNavigationContent>
        <Suspense fallback={<BrandGridSkeleton />}>
          <BrandGridLoader />
        </Suspense>
      </BrandNavigationContent>
    );

    return (
      <BrandNavigationTrigger title={title}>
        {brandContent}
      </BrandNavigationTrigger>
    );
  }

  // Fallback for non-brand layouts or future layout types
  return null;
}
