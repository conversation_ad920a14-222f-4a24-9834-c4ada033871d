import React from "react";
import Link from "next/link";
import { resolveHref } from "@/lib/utils";
import { ChevronRight } from "lucide-react";
import AnimatedText from "../../shared/animated-text";
import { Button } from "../../ui/button";
import {
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu";
import type { NavigationItem } from "@/lib/navigation-utils";
import { processNavigationItems } from "@/lib/navigation-utils";
import type { PageHierarchy } from "@/lib/page-utils";
import { buildPagePath } from "@/lib/page-utils";
import type { ButtonType } from "@/types";
import { getNavigationLayoutType, getGroupNavigationLayoutType } from "@/lib/navigation-layout";
import { NavigationLayout } from "./navigation-layout";
import type { NavigationSettingsQueryResult } from "../../../../sanity.types";

interface NavigationItemsProps {
  navigationSettings: NavigationSettingsQueryResult;
  pathname: string;
}

/**
 * Server component that processes and renders navigation items
 * This handles all the server-side logic and data fetching
 */
export function NavigationItems({ navigationSettings, pathname }: NavigationItemsProps) {
  const { navbarMenuItems } = navigationSettings?.navbar as unknown as {
    navbarMenuItems: NavigationItem[] | null;
  };

  // Process navigation items to ensure proper nested URLs
  const processedNavItems = processNavigationItems(navbarMenuItems || []);

  return (
    <>
      {processedNavItems.map(
        (
          item: NavigationItem & {
            isButton?: boolean;
            _key?: string;
            title?: string;
            showChildren?: boolean;
          },
        ) => (
          <React.Fragment key={item._key}>
            {!item.isButton ? (
              <>
                {item.menuItemType === "group" ? (
                  // Check if any page reference in the group has a custom layout
                  getGroupNavigationLayoutType(item.pageReferences) ? (
                    <NavigationLayout 
                      title={item.title}
                      layoutType={getGroupNavigationLayoutType(item.pageReferences)}
                    />
                  ) : (
                    // Default group rendering
                    <NavigationMenuItem>
                      <NavigationMenuTrigger className="group-hover/nav:opacity-40 hover:opacity-100! bg-transparent">
                        {item.title}
                      </NavigationMenuTrigger>
                      <NavigationMenuContent className="min-w-48">
                        {item.pageReferences?.map((page) => {
                          const pageUrl = resolveHref(
                            page._type,
                            (page.slug as unknown as string) ?? "",
                          );
                          return (
                            <Link
                              key={page._id}
                              href={pageUrl ?? "/"}
                              className="group py-1 pl-3 pr-2 flex items-center justify-between gap-6 rounded-md border border-dashed hover:bg-card"
                            >
                              {page.title || page.name}
                              <ChevronRight
                                size={14}
                                className="text-muted-foreground group-hover:-translate-x-0.5 group-hover:text-accent-foreground transition-all duration-300"
                              />
                            </Link>
                          );
                        })}
                      </NavigationMenuContent>
                    </NavigationMenuItem>
                  )
                ) : (
                  // Check if single item has a custom layout
                  getNavigationLayoutType(item.pageReference) ? (
                    <NavigationLayout 
                      title={item.pageReference?.title || item.title}
                      layoutType={getNavigationLayoutType(item.pageReference)}
                    />
                  ) : (
                    // Default single item rendering
                    <NavigationMenuItem>
                      {/* Handle single menu item with potential children */}
                      {item.showChildren &&
                      item.pageReference?.children &&
                      item.pageReference.children.length > 0 ? (
                        <>
                          <NavigationMenuTrigger className="group-hover/nav:opacity-40 hover:opacity-100!">
                            {item.pageReference.title}
                          </NavigationMenuTrigger>
                          <NavigationMenuContent>
                            {/* Child pages as dropdown items */}
                            {item.pageReference.children.map(
                              (pageChild: unknown) => {
                                const child: PageHierarchy =
                                  pageChild as unknown as PageHierarchy;
                                const childUrl = buildPagePath(child);
                                return (
                                  <Link
                                    key={child._id}
                                    href={childUrl}
                                    className="group py-1 pl-3 pr-2 flex items-center justify-between gap-6 rounded-md border border-dashed hover:bg-card"
                                  >
                                    {child.title}
                                    <ChevronRight
                                      size={14}
                                      className="text-muted-foreground group-hover:-translate-x-0.5 group-hover:text-accent-foreground transition-all duration-300"
                                    />
                                  </Link>
                                );
                              },
                            )}
                          </NavigationMenuContent>
                        </>
                      ) : item.showChildren ? (
                        <>
                          <NavigationMenuTrigger className="group-hover/nav:opacity-40 hover:opacity-100!">
                            {item.pageReference?.title || item.title}
                          </NavigationMenuTrigger>
                          <NavigationMenuContent>
                            <div className="py-1 pl-3 pr-2 text-sm text-muted-foreground italic">
                              No child pages yet
                            </div>
                          </NavigationMenuContent>
                        </>
                      ) : (
                        /* Regular single menu item */
                        <Link
                          href={
                            resolveHref(
                              item?.pageReference?._type ?? "",
                              (item?.pageReference
                                ?.slug as unknown as string) ?? "",
                            ) ?? "/"
                          }
                          className={`relative overflow-hidden inline-flex transition-opacity duration-200 group-hover/nav:opacity-40 hover:opacity-100! ${
                            !item.isButton ? "hover:underline underline-offset-38" : ""
                          } ${
                            item.isButton ? "py-2 px-4 rounded-full text-foreground bg-primary" : ""
                          } ${
                            pathname.includes(`/${item.pageReference?.slug ?? ""}`) ? "text-primary-hover" : ""
                          }`}
                        >
                          <AnimatedText>
                            {item.pageReference?.title || item.title}
                          </AnimatedText>
                        </Link>
                      )}
                    </NavigationMenuItem>
                  )
                )}
              </>
            ) : (
              <NavigationMenuItem>
                <Button
                  variant="primary"
                  disableIcon={true}
                  buttonType="internal"
                  pageReference={
                    item.pageReference as unknown as ButtonType["buttonPageReference"]
                  }
                >
                  {item.title}
                </Button>
              </NavigationMenuItem>
            )}
          </React.Fragment>
        ),
      )}
    </>
  );
}
