/**
 * Loading skeleton for brand grid
 */
export function BrandGridSkeleton() {
  return (
    <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
      {Array.from({ length: 12 }).map((_, index) => (
        <div key={index} className="animate-pulse">
          <div className="p-4 rounded-lg border">
            <div className="text-center">
              {/* Logo skeleton */}
              <div className="w-12 h-12 mx-auto mb-2 bg-gray-200 dark:bg-gray-700 rounded"></div>
              {/* Brand name skeleton */}
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-1"></div>
              {/* Tagline skeleton */}
              <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mx-auto"></div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
