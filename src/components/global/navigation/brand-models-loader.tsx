import { sanityFetch } from '@/sanity/lib/live';
import { brandFeaturedModelsQuery } from '@/sanity/lib/queries/fragments/navigation';

interface BrandModelsLoaderProps {
  brandId: string;
}

/**
 * Server component that fetches and displays featured models for a brand
 * Each component fetches its own data using centralized query
 */
export async function BrandModelsLoader({ brandId }: BrandModelsLoaderProps) {
  // Each component fetches its own data using centralized query
  const { data: models } = await sanityFetch({ 
    query: brandFeaturedModelsQuery,
    params: { brandId },
    revalidate: 1800 // 30 minutes cache
  });

  if (!models?.length) {
    return <div className="text-xs text-muted-foreground">No featured models</div>;
  }

  return (
    <div className="space-y-2">
      {models.map((model) => (
        <div key={model._id} className="text-xs">
          <div className="font-medium">{model.name}</div>
          <div className="text-muted-foreground">{model.modelYear}</div>
        </div>
      ))}
    </div>
  );
}
