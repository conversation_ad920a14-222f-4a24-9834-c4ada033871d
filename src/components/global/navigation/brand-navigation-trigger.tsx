"use client";

import { ReactNode } from 'react';
import { 
  NavigationMenuItem, 
  NavigationMenuTrigger, 
  NavigationMenuContent 
} from '@/components/ui/navigation-menu';

interface BrandNavigationTriggerProps {
  title: string;
  children: ReactNode; // Server components passed as children
}

/**
 * Client component that handles hover interactions for brand navigation
 * Receives server components as children props following Next.js 15 composition pattern
 */
export function BrandNavigationTrigger({ title, children }: BrandNavigationTriggerProps) {
  return (
    <NavigationMenuItem>
      <NavigationMenuTrigger className="group-hover/nav:opacity-40 hover:opacity-100! bg-transparent">
        {title}
      </NavigationMenuTrigger>
      <NavigationMenuContent className="w-screen max-w-none">
        {children}
      </NavigationMenuContent>
    </NavigationMenuItem>
  );
}
