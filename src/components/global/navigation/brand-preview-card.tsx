import { Suspense } from 'react';
import Link from 'next/link';
import { BrandModelsLoader } from './brand-models-loader';
import { BrandOffersLoader } from './brand-offers-loader';

interface BrandPreviewCardProps {
  brand: {
    _id: string;
    name: string;
    slug: string;
    tagline?: string;
    logo?: {
      asset: {
        url: string;
      };
    };
    brandColors?: {
      primaryColor?: string;
      secondaryColor?: string;
      accentColor?: string;
    };
  };
}

/**
 * Server component that renders individual brand cards with hover details
 */
export function BrandPreviewCard({ brand }: BrandPreviewCardProps) {
  return (
    <div className="group relative">
      <Link 
        href={`/gammes/${brand.slug}`}
        className="block p-4 rounded-lg border hover:shadow-md transition-all"
        style={{
          '--brand-primary': brand.brandColors?.primaryColor || '#000',
          '--brand-accent': brand.brandColors?.accentColor || '#666'
        } as React.CSSProperties}
      >
        {/* Brand logo and basic info */}
        <div className="text-center">
          {brand.logo?.asset?.url && (
            <img 
              src={brand.logo.asset.url} 
              alt={brand.name}
              className="w-12 h-12 mx-auto mb-2 object-contain"
            />
          )}
          <h4 className="font-medium text-sm">{brand.name}</h4>
          {brand.tagline && (
            <p className="text-xs text-muted-foreground mt-1">{brand.tagline}</p>
          )}
        </div>
      </Link>

      {/* Hover details - loaded separately */}
      <div className="absolute top-full left-0 w-80 bg-background border border-border shadow-lg rounded-lg p-4 opacity-0 group-hover:opacity-100 transition-opacity z-50 pointer-events-none group-hover:pointer-events-auto">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <h5 className="font-medium mb-2 text-sm">Featured Models</h5>
            <Suspense fallback={<div className="text-xs text-muted-foreground">Loading...</div>}>
              <BrandModelsLoader brandId={brand._id} />
            </Suspense>
          </div>
          
          <div>
            <h5 className="font-medium mb-2 text-sm">Current Offers</h5>
            <Suspense fallback={<div className="text-xs text-muted-foreground">Loading...</div>}>
              <BrandOffersLoader brandId={brand._id} />
            </Suspense>
          </div>
        </div>
      </div>
    </div>
  );
}
