import { sanityFetch } from '@/sanity/lib/live';
import { brandFeaturedOffersQuery } from '@/sanity/lib/queries/fragments/navigation';

interface BrandOffersLoaderProps {
  brandId: string;
}

/**
 * Server component that fetches and displays featured offers for a brand
 * Each component fetches its own data using centralized query
 */
export async function BrandOffersLoader({ brandId }: BrandOffersLoaderProps) {
  // Each component fetches its own data using centralized query
  const { data: offers } = await sanityFetch({ 
    query: brandFeaturedOffersQuery,
    params: { brandId },
    revalidate: 900 // 15 minutes cache
  });

  if (!offers?.length) {
    return <div className="text-xs text-muted-foreground">No current offers</div>;
  }

  return (
    <div className="space-y-2">
      {offers.map((offer) => (
        <div key={offer._id} className="text-xs">
          <div className="font-medium">{offer.title}</div>
          {offer.savings && (
            <div className="text-green-600">{offer.savings}</div>
          )}
        </div>
      ))}
    </div>
  );
}
