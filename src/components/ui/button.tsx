import Link from "next/link";
import * as React from "react";
import type { ButtonType } from "@/types";
import { ArrowRight } from "lucide-react";
import { cn, getAnchorHref, resolveHref } from "@/lib/utils";
import { cva, type VariantProps } from "class-variance-authority";
import { Slot } from "@radix-ui/react-slot";

const buttonVariants = cva(
  // Base classes: consistent shape, font, and transitions for all buttons
  "cursor-pointer group relative px-4 md:px-6 inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-full text-xs md:text-sm font-semibold transition-all duration-300 disabled:pointer-events-none disabled:opacity-50 overflow-hidden active:scale-[0.98] focus-visible:outline-none",
  {
    variants: {
      variant: {
        // Primary: The main call-to-action. Metallic gradient with a shimmer effect.
        primary: [
          "text-primary-foreground border border-transparent",
          "bg-gradient-to-br from-silver-300 to-silver-600",
          "dark:from-silver-100 dark:to-silver-500",
          "hover:shadow-premium-medium hover:-translate-y-0.5",
          // Shimmer effect
          "before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/30 before:to-transparent before:-translate-x-full hover:before:translate-x-full before:transition-transform before:duration-500",
          "focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background",
        ],

        // Secondary: Apple-style glassmorphism. Semi-transparent with a blurred background.
        secondary: [
          "bg-glass-medium text-foreground border border-white/10 dark:border-white/20 backdrop-blur-md",
          "hover:bg-glass-light hover:border-white/20 dark:hover:border-white/30 hover:shadow-premium-soft hover:-translate-y-0.5",
          "focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background",
        ],

        // Tertiary: A lower-emphasis button that stands out on hover with a gradient.
        tertiary: [
          "bg-surface-container text-foreground border border-border",
          "hover:bg-gradient-titanium hover:text-silver-950 hover:border-titanium-accent hover:shadow-premium-soft hover:-translate-y-0.5",
          "focus-visible:ring-2 focus-visible:ring-titanium-accent focus-visible:ring-offset-2 focus-visible:ring-offset-background",
        ],

        // Outline (Dashed): For secondary actions that need to be subtle.
        outline: [
          "bg-transparent text-foreground border border-outline border-dashed",
          "hover:bg-accent hover:text-accent-foreground hover:shadow-premium-soft hover:-translate-y-0.5",
          "focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background",
        ],

        // Outline (Solid): A more standard outline button.
        "outline-solid": [
          "bg-silver-100/20 dark:bg-silver-900/20 text-foreground border border-outline",
          "hover:bg-accent hover:text-accent-foreground hover:shadow-premium-soft hover:-translate-y-0.5",
          "focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background",
        ],

        // Underline: Used for text-like links that need to act like buttons.
        underline: [
          "px-0 bg-transparent border-none underline underline-offset-8 decoration-2 decoration-silver-600",
          "hover:decoration-primary",
          "focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background",
        ],

        // Specialized automotive gradient variants
        chrome: [
          "bg-gradient-chrome text-silver-950 border-transparent",
          "hover:shadow-premium-hard hover:-translate-y-1 hover:-translate-y-0.5",
          "before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/30 before:to-transparent before:-translate-x-full hover:before:translate-x-full before:transition-transform before:duration-700",
        ],
        titanium: [
          "bg-gradient-titanium text-silver-950 border-transparent",
          "hover:shadow-premium-medium hover:-translate-y-0.5 hover:brightness-110",
        ],
        carbon: [
          "bg-gradient-carbon text-silver-100 border-transparent",
          "hover:shadow-premium-medium hover:-translate-y-0.5 hover:brightness-125",
        ],
        copper: [
          "bg-gradient-copper text-silver-950 border-transparent",
          "hover:shadow-premium-medium hover:-translate-y-0.5 hover:brightness-110",
        ],
      },
      size: {
        default: "h-10 md:h-11 px-4 md:px-6",
        sm: "h-9 px-3 text-xs",
        lg: "h-12 md:h-14 px-6 md:px-8 text-sm md:text-base",
        icon: "h-10 w-10 p-0",
      },
      width: {
        auto: "w-auto",
        full: "w-full",
      },
    },
    defaultVariants: {
      variant: "primary",
      size: "default",
      width: "auto",
    },
  },
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLAnchorElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
  disableIcon?: boolean;
  pageReference?: ButtonType["buttonPageReference"];
  externalUrl?: ButtonType["buttonExternalUrl"];
  fileUrl?: ButtonType["buttonExternalUrl"];
  buttonType?: ButtonType["buttonType"];
  emailAddress?: ButtonType["buttonEmailAddress"];
  anchorLocation?: ButtonType["buttonAnchorLocation"];
  anchorId?: ButtonType["buttonAnchorId"];
}

const Button = React.forwardRef<HTMLAnchorElement, ButtonProps>(
  (
    {
      children,
      className,
      variant,
      size,
      width,
      asChild = false,
      disableIcon,
      pageReference,
      externalUrl,
      emailAddress,
      fileUrl,
      buttonType,
      anchorLocation,
      anchorId,
      ...props
    },
    ref,
  ) => {
    const baseClasses = cn(buttonVariants({ variant, size, width, className }));

    const content = (
      <>
        {children}
        {!disableIcon && <ButtonIcon />}
      </>
    );

    // Default button rendering
    if (!buttonType) {
      const Comp = asChild ? Slot : "button";
      return (
        <Comp
          className={cn(
            baseClasses,
            className?.includes("brand") &&
              variant !== "underline" && [
                "bg-[var(--brand-color-primary)]",
                "text-background dark:text-foreground",
              ],
            className?.includes("brand") &&
              variant === "underline" && [
                "hover:decoration-[var(--brand-color-primary)]",
                "hover:text-[var(--brand-color-primary)]",
              ],
          )}
          // @ts-expect-error: incompatible types
          ref={ref}
          {...props}
        >
          {children}
        </Comp>
      );
    }

    // Link-based button rendering
    let href = "/";
    const linkProps: React.AnchorHTMLAttributes<HTMLAnchorElement> = {};

    switch (buttonType) {
      case "internal":
        if (!pageReference) return null;
        href =
          resolveHref(pageReference._type, pageReference.slug ?? "") ?? "/";
        break;
      case "anchor":
        href = getAnchorHref({
          anchorLocation: anchorLocation ?? "currentPage",
          anchorId: anchorId ?? "",
          pageReference: pageReference ?? null,
        });
        break;
      case "external":
        href = externalUrl ?? "/";
        linkProps.rel = "noopener noreferrer";
        linkProps.target = "_blank";
        break;
      case "fileDownload":
        href = fileUrl ?? "";
        linkProps.download = true;
        break;
      case "emailAddress":
        href = `mailto:${emailAddress}`;
        break;
    }

    if (asChild) {
      return (
        <Slot className={baseClasses} {...props} ref={ref}>
          {children}
        </Slot>
      );
    }

    return (
      <Link
        href={href}
        ref={ref}
        className={cn(baseClasses, {
          "bg-[var(--brand-color-primary)]":
            className?.includes("brand") && variant !== "underline",
          "text-foreground": className?.includes("brand"),
          "hover:decoration-[var(--brand-color-primary)]":
            className?.includes("brand") && variant === "underline",
        })}
        {...linkProps}
        {...props}
      >
        {content}
      </Link>
    );
  },
);

Button.displayName = "Button";

// Enhanced button icon with premium styling
function ButtonIcon() {
  return (
    <ArrowRight
      size={16}
      className="transition-transform duration-300 group-hover:translate-x-1 group-hover:scale-110"
    />
  );
}

export { Button, buttonVariants };
