import Link from "next/link";
import Image from "next/image";
import { useMemo } from "react";
import { Clock, Gift, Star, ChevronRight, Calendar } from "lucide-react";
import { cn, isWithinDays } from "@/lib/utils";
import { getBrandColor } from "@/components/brands/models/utils";
import AnimatedUnderline from "@/components/shared/animated-underline";
import { Badge } from "@/components/ui/badge";
import type { OfferBySlugQueryResult } from "../../../sanity.types";

type Offer = NonNullable<OfferBySlugQueryResult>;

interface OfferCardProps {
  offer: Offer;
  priority?: "high" | "normal";
  variant?: "grid" | "list" | "featured";
}

export default function OfferCard({
  offer,
  priority = "normal",
  variant = "grid",
}: OfferCardProps) {
  const brandColor = getBrandColor(offer.brand?.brandColors?.primaryColor);

  // Check if the offer is expiring soon (within 7 days)
  const isExpiringSoon = useMemo(
    () =>
      offer.validityPeriod?.validTo &&
      isWithinDays(new Date(offer.validityPeriod.validTo), 7),
    [offer.validityPeriod],
  );

  const redemptionPercentage = offer.maxRedemptions
    ? ((offer.currentRedemptions || 0) / offer.maxRedemptions) * 100
    : 0;

  return (
    <article
      aria-label={`${offer.brand?.name || ""} ${offer.title}`}
      className={cn(
        "relative group pb-8 border-b border-dashed h-full flex flex-col",
        variant === "featured"
          ? "col-span-12 md:col-span-12 lg:col-span-8"
          : "col-span-12 md:col-span-6 lg:col-span-4",
      )}
      style={{ "--brand-color-primary": brandColor } as React.CSSProperties}
    >
      <Link
        href={`/offres/${offer.slug}`}
        className="relative flex-1 flex flex-col"
      >
        {/* Brand Logo Overlay */}
        {/* @ts-expect-error weak reference */}
        {offer.brand?.logo?.asset?.url && (
          <div className="z-10 absolute top-6 left-4 px-2 py-1">
            <div className="w-12 h-12 bg-background/30 backdrop-blur-sm rounded-lg p-2 border">
              <Image
                // @ts-expect-error weak reference
                src={offer.brand.logo.asset.url}
                alt={`${offer.brand.name} logo`}
                width={32}
                height={32}
                className="w-full h-full object-contain"
              />
            </div>
          </div>
        )}

        {/* Offer Type Badge */}
        {offer.offerType && (
          <div className="z-10 absolute top-6 right-6 px-2 py-1 rounded-md text-xs font-medium bg-background/30 backdrop-blur-sm border">
            {offer.offerType}
          </div>
        )}

        {/* Featured Badge */}
        {offer.displaySettings?.isFeatured && (
          <div
            className="z-10 absolute top-20 right-6 px-2 py-1 rounded-md text-xs font-medium text-white shadow-md"
            style={{ backgroundColor: brandColor }}
          >
            <Star className="inline-block mr-1 h-3 w-3" />
            Featured
          </div>
        )}

        {/* Main Image */}
        <div className="h-72 p-4 rounded-3xl border border-dashed backdrop-blur-md backdrop-opacity-50 transition-all duration-200 ease-out group-hover:border-[var(--brand-color-primary)]">
          {offer.mainImage?.asset?.url ? (
            <Image
              src={offer.mainImage.asset.url}
              alt={offer.mainImage.altText || (offer.title as string)}
              width={800}
              height={600}
              className="aspect-auto w-full h-full rounded-2xl object-cover"
              priority={priority === "high"}
              placeholder={
                offer.mainImage.asset.metadata?.blurHash ? "blur" : undefined
              }
              blurDataURL={
                offer.mainImage.asset.metadata?.blurHash ?? undefined
              }
            />
          ) : (
            <div className="h-full rounded-2xl flex items-center justify-center">
              <div className="text-center text-muted-foreground">
                <div className="w-12 h-12 mx-auto mb-2 bg-muted rounded-lg flex items-center justify-center">
                  <Gift className="h-6 w-6" />
                </div>
                <div className="text-sm">No Image Available</div>
              </div>
            </div>
          )}
        </div>

        {/* Content */}
        <div className="mt-5 md:mt-6 relative flex flex-col flex-1 px-4">
          {/* Validity Badge */}
          {offer.validityPeriod?.isLimitedTime && (
            <div
              className={cn(
                "flex items-center gap-1 text-xs mb-2",
                isExpiringSoon
                  ? "text-orange-500 font-medium"
                  : "text-muted-foreground",
              )}
            >
              <Clock className="h-3 w-3" />
              <span>
                {isExpiringSoon
                  ? `Expiring Soon: ${new Date(offer.validityPeriod.validTo as string).toLocaleDateString("fr-CH")}`
                  : offer.validityPeriod.validTo
                    ? `Expires ${new Date(offer.validityPeriod.validTo).toLocaleDateString("fr-CH")}`
                    : "Limited Time Offer"}
              </span>
            </div>
          )}

          {/* Top content that can grow */}
          <div className="space-y-4 flex-1">
            {/* Title */}
            <div>
              <div className="flex items-center gap-2 text-sm text-muted-foreground mb-1">
                <span className="font-medium">{offer.brand?.name}</span>
                {offer.validityPeriod?.validFrom && (
                  <>
                    <span>•</span>
                    <span className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      {new Date(
                        offer.validityPeriod.validFrom,
                      ).toLocaleDateString("fr-CH")}
                    </span>
                  </>
                )}
              </div>
              <h2
                className="text-xl font-bold text-balance group-hover:text-primary transition-colors"
                style={{ color: brandColor }}
              >
                {offer.title}
              </h2>
            </div>

            {/* Short Description */}
            {offer.shortDescription && (
              <p className="text-sm text-muted-foreground line-clamp-2 text-balance">
                {offer.shortDescription}
              </p>
            )}

            {/* Key Benefits Preview */}
            {offer.keyBenefits && offer.keyBenefits.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {offer.keyBenefits.slice(0, 3).map((benefit, index) => (
                  <Badge
                    key={index}
                    variant="outline"
                    className="text-xs"
                    style={{
                      borderColor: `${brandColor}40`,
                      color: brandColor,
                    }}
                  >
                    {benefit.title}
                  </Badge>
                ))}
              </div>
            )}

            {/* Financial Highlights */}
            {(offer.financingDetails ||
              (offer.savings &&
                ((typeof offer.savings.discountValue === "number" &&
                  offer.savings.discountValue > 0) ||
                  (typeof offer.savings.discountPercentage === "number" &&
                    offer.savings.discountPercentage > 0)))) && (
              <div className="flex items-center justify-between text-sm">
                {offer.savings &&
                  ((typeof offer.savings.discountValue === "number" &&
                    offer.savings.discountValue > 0) ||
                    (typeof offer.savings.discountPercentage === "number" &&
                      offer.savings.discountPercentage > 0)) && (
                    <Badge
                      variant="outline"
                      style={{
                        borderColor: `${brandColor}40`,
                        color: brandColor,
                      }}
                    >
                      {offer.savings.discountType === "percentage"
                        ? `${offer.savings.discountPercentage}% OFF`
                        : `CHF ${offer.savings.discountValue} OFF`}
                    </Badge>
                  )}
              </div>
            )}
          </div>

          {/* Redemption Progress */}
          {offer.maxRedemptions && (
            <div className="pt-4 mt-auto">
              <div className="flex justify-between text-xs text-muted-foreground mb-2">
                <span>
                  Claimed: {offer.currentRedemptions || 0}/
                  {offer.maxRedemptions}
                </span>
                <span>{redemptionPercentage.toFixed(0)}% claimed</span>
              </div>
              <div className="w-full h-1.5 bg-muted rounded-full overflow-hidden">
                <div
                  className="h-full rounded-full transition-all duration-300"
                  style={{
                    width: `${redemptionPercentage}%`,
                    backgroundColor: brandColor,
                  }}
                ></div>
              </div>
            </div>
          )}

          {/* Arrow */}
          <ChevronRight
            size={18}
            className="-translate-x-6 opacity-0 group-hover:-translate-x-0 group-hover:opacity-100 transition-all duration-300 text-muted-foreground absolute bottom-10 right-4"
          />
        </div>
      </Link>
      <AnimatedUnderline
        className={"-translate-y-0.5"}
        style={{ backgroundColor: brandColor }}
      />
    </article>
  );
}
