/**
 * ---------------------------------------------------------------------------------
 * This file has been generated by Sanity TypeGen.
 * Command: `sanity typegen generate`
 *
 * Any modifications made directly to this file will be overwritten the next time
 * the TypeScript definitions are generated. Please make changes to the Sanity
 * schema definitions and/or GROQ queries if you need to update these types.
 *
 * For more information on how to use Sanity TypeGen, visit the official documentation:
 * https://www.sanity.io/docs/sanity-typegen
 * ---------------------------------------------------------------------------------
 */

// Source: schema.json
export type ConditionTypeArray = Array<string>;

export type ConditionType = {
  _type: "conditionType";
  value?: "new" | "used" | "demonstration" | "oldtimer" | "pre-registered";
};

export type FuelTypeArray = Array<string>;

export type FuelType = {
  _type: "fuelType";
  value?: "cng-petrol" | "diesel" | "electric" | "ethanol-petrol" | "hev-diesel" | "hev-petrol" | "hydrogen" | "lpg-petrol" | "mhev-diesel" | "mhev-petrol" | "petrol" | "phev-diesel" | "phev-petrol" | "two-stroke-mixture";
};

export type TimelineStep = {
  _type: "timelineStep";
  title?: string;
  description?: string;
  content?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "blockquote";
    listItem?: "bullet" | "number";
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  } | {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
    _key: string;
  }>;
  status?: "completed" | "in-progress" | "pending" | "blocked";
  isMilestone?: boolean;
  icon?: {
    type?: "number" | "lucide" | "image" | "none";
    number?: number;
    lucideIcon?: string;
    customImage?: {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      _type: "image";
    };
  };
  timeline?: {
    startDate?: string;
    endDate?: string;
    duration?: string;
    assignee?: {
      name?: string;
      role?: string;
      avatar?: {
        asset?: {
          _ref: string;
          _type: "reference";
          _weak?: boolean;
          [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
        };
        media?: unknown;
        hotspot?: SanityImageHotspot;
        crop?: SanityImageCrop;
        _type: "image";
      };
    };
  };
  customColor?: "default" | "blue" | "green" | "orange" | "red" | "purple";
  progress?: number;
};

export type FilterItem = {
  _type: "filterItem";
  filterType?: "price" | "make" | "model" | "condition" | "fuel" | "year" | "mileage" | "transmission" | "bodyType" | "driveType" | "color" | "emission";
  defaultOpen?: boolean;
  title?: string;
  defaultValue?: {
    priceFrom?: number;
    priceTo?: number;
    makeKey?: Array<string>;
    modelKey?: Array<string>;
    yearFrom?: number;
    yearTo?: number;
    mileageFrom?: number;
    mileageTo?: number;
    conditionType?: Array<string>;
    fuelType?: Array<string>;
  };
};

export type ContentItem = {
  _type: "contentItem";
  type?: "manual" | "reference";
  title?: string;
  excerpt?: string;
  image?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    alt?: string;
    _type: "image";
  };
  reference?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "post";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "project";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "service";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "page";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "testimonial";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "brand";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "vehicleModel";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "offer";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "center";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "b2bSolution";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "fleet";
  };
  categories?: Array<string>;
  tags?: Array<string>;
  featured?: boolean;
  link?: {
    label?: string;
    url?: string;
    openInNewTab?: boolean;
  };
  metadata?: {
    author?: string;
    publishDate?: string;
    priority?: number;
    status?: "published" | "draft" | "archived";
  };
};

export type VideoObject = {
  _type: "videoObject";
  title?: string;
  videoUrl?: string;
};

export type CallToActionObject = {
  _type: "callToActionObject";
  callToActionTitle?: string;
  callToActionParagraph?: string;
  buttons?: Array<{
    _key: string;
  } & ButtonObject>;
};

export type SpacerObject = {
  _type: "spacerObject";
  spacing?: "none" | "small" | "medium" | "large";
};

export type SingleImageObject = {
  _type: "singleImageObject";
  image?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    altText?: string;
    aspectRatio?: "square" | "rectangle" | "portrait";
    _type: "image";
  };
};

export type ButtonObject = {
  _type: "buttonObject";
  showButton?: boolean;
  buttonText?: string;
  buttonType?: "internal" | "anchor" | "external" | "fileDownload" | "emailAddress";
  buttonAnchorLocation?: "currentPage" | "choosePage";
  buttonPageReference?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "page";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "blogPage";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "servicesPage";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "projectsPage";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "service";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "project";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "center";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "fleet";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "brand";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "b2bSolutionsPage";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "fleetPage";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "brandsPage";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "searchPage";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "offersPage";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "offer";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "b2bSolution";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "vehicleModel";
  };
  buttonAnchorId?: string;
  buttonExternalUrl?: string;
  buttonEmailAddress?: string;
  buttonFileUrl?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.fileAsset";
    };
    media?: unknown;
    _type: "file";
  };
  buttonVariant?: "primary" | "secondary" | "tertiary" | "outline-solid" | "underline";
  buttonWidth?: "auto" | "fullWidth";
};

export type HeadingObject = {
  _type: "headingObject";
  headingText?: string;
  headingTag?: "h2" | "h3" | "h4" | "h5" | "h6";
  headingSize?: "xxxl" | "xxl" | "xl" | "lg" | "md" | "sm" | "xs";
};

export type VehicleListBlock = {
  _type: "vehicleListBlock";
  title?: string;
  subtitle?: string;
  ctaButton?: {
    text?: string;
    url?: string;
    style?: "primary" | "secondary" | "outline";
  };
  layout?: "grid" | "list" | "carousel" | "table";
  cardVariant?: "default" | "featured" | "compact" | "minimal" | "horizontal" | "thumbnail";
  itemsPerView?: number;
  itemsPerRow?: number;
  aspectRatio?: "4:3" | "16:9" | "auto";
  spacing?: "tight" | "normal" | "loose";
  preFilters?: {
    centerId?: number;
    makeKey?: Array<string>;
    modelKey?: Array<string>;
    fuelType?: FuelTypeArray;
    conditionType?: ConditionTypeArray;
    priceFrom?: number;
    priceTo?: number;
  };
  enabledFilters?: Array<string>;
  filterLayout?: "sidebar" | "top" | "modal";
  theme?: "default" | "minimal" | "card" | "compact";
  brandIntegration?: boolean;
};

export type CarouselBlock = {
  _type: "carouselBlock";
  title?: string;
  description?: string;
  carouselConfig?: CarouselConfig;
  contentType?: "mixed" | "images" | "videos" | "cards" | "blocks" | "dataSource";
  dataSource?: DataSource;
  slides?: Array<{
    slideType?: "image" | "video" | "card" | "source";
    image?: {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      alt?: string;
      caption?: string;
      _type: "image";
    };
    video?: {
      videoFile?: {
        asset?: {
          _ref: string;
          _type: "reference";
          _weak?: boolean;
          [internalGroqTypeReferenceTo]?: "sanity.fileAsset";
        };
        media?: unknown;
        _type: "file";
      };
      thumbnail?: {
        asset?: {
          _ref: string;
          _type: "reference";
          _weak?: boolean;
          [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
        };
        media?: unknown;
        hotspot?: SanityImageHotspot;
        crop?: SanityImageCrop;
        _type: "image";
      };
      caption?: string;
    };
    content?: Array<{
      children?: Array<{
        marks?: Array<string>;
        text?: string;
        _type: "span";
        _key: string;
      }>;
      style?: "normal" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "blockquote";
      listItem?: "bullet" | "number";
      markDefs?: Array<{
        href?: string;
        _type: "link";
        _key: string;
      }>;
      level?: number;
      _type: "block";
      _key: string;
    }>;
    _type: "mixedSlide";
    _key: string;
  } | {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    alt?: string;
    caption?: string;
    _type: "imageSlide";
    _key: string;
  } | {
    videoFile?: {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.fileAsset";
      };
      media?: unknown;
      _type: "file";
    };
    thumbnail?: {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      _type: "image";
    };
    caption?: string;
    _type: "videoSlide";
    _key: string;
  } | {
    title?: string;
    description?: string;
    image?: {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      alt?: string;
      _type: "image";
    };
    button?: {
      text?: string;
      url?: string;
      style?: "primary" | "secondary" | "outline-solid";
    };
    _type: "cardSlide";
    _key: string;
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "heroBlock";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "featureCardsBlock";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "testimonialBlock";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "callToActionBlock";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "freeformBlock";
  }>;
  anchorId?: string;
};

export type CarouselConfig = {
  _type: "carouselConfig";
  orientation?: "horizontal" | "vertical";
  showNavigation?: boolean;
  showDots?: boolean;
  showThumbnails?: boolean;
  emblaOptions?: {
    loop?: boolean;
    align?: "start" | "center" | "end";
    slidesToScroll?: "auto" | "1" | "2" | "3";
    dragFree?: boolean;
  };
  slidesDisplay?: {
    slidesPerView?: {
      mobile?: number;
      tablet?: number;
      desktop?: number;
    };
    slideSpacing?: "0" | "1rem" | "1.5rem" | "2rem";
  };
  autoplay?: {
    enabled?: boolean;
    delay?: number;
    stopOnInteraction?: boolean;
  };
  transitionEffect?: {
    type?: "slide" | "fade";
    duration?: number;
  };
};

export type StatisticsBlock = {
  _type: "statisticsBlock";
  title?: string;
  description?: string;
  statistics?: Array<{
    label?: string;
    description?: string;
    dataSource?: DataSource;
    numberFormatting?: {
      prefix?: string;
      suffix?: string;
      decimalPlaces?: number;
      useThousandsSeparator?: boolean;
      abbreviateLargeNumbers?: boolean;
    };
    animation?: AnimationConfig;
    icon?: {
      iconType?: "none" | "lucide" | "image" | "emoji";
      lucideIcon?: string;
      customImage?: {
        asset?: {
          _ref: string;
          _type: "reference";
          _weak?: boolean;
          [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
        };
        media?: unknown;
        hotspot?: SanityImageHotspot;
        crop?: SanityImageCrop;
        _type: "image";
      };
      emoji?: string;
      position?: "above" | "left" | "right";
    };
    styling?: {
      numberColor?: SimplerColor;
      labelColor?: SimplerColor;
      numberSize?: "text-2xl" | "text-3xl" | "text-4xl" | "text-5xl" | "text-6xl";
      fontWeight?: "font-normal" | "font-medium" | "font-semibold" | "font-bold" | "font-extrabold";
    };
    _type: "statistic";
    _key: string;
  }>;
  layout?: {
    columns?: {
      mobile?: number;
      tablet?: number;
      desktop?: number;
    };
    spacing?: "4" | "6" | "8" | "12";
    alignment?: "left" | "center" | "right";
  };
  globalAnimation?: {
    enableStagger?: boolean;
    staggerDelay?: number;
  };
  backgroundColor?: SimplerColor;
  anchorId?: string;
};

export type ProcessTimelinesBlock = {
  _type: "processTimelinesBlock";
  title?: string;
  subtitle?: string;
  steps?: Array<{
    _key: string;
  } & TimelineStep>;
  timelineConfig?: TimelineConfig;
  showOverallProgress?: boolean;
  backgroundColor?: SimplerColor;
  accentColor?: SimplerColor;
  anchorId?: string;
};

export type TimelineConfig = {
  _type: "timelineConfig";
  layout?: "vertical" | "horizontal" | "alternating" | "stepped";
  visualStyle?: {
    dotStyle?: "circle" | "square" | "diamond";
    lineStyle?: "solid" | "dashed" | "dotted" | "gradient";
    cardStyle?: "clean" | "bordered" | "shadowed" | "glass";
    colorScheme?: "default" | "monochrome" | "colorful" | "brand";
  };
  animations?: {
    enableAnimations?: boolean;
    animationType?: "fadeIn" | "slideIn" | "scaleIn" | "progressive";
    staggerDelay?: number;
    enableScrollProgress?: boolean;
  };
  responsiveSettings?: {
    mobileLayout?: "same" | "vertical" | "compact";
    hideElementsOnMobile?: Array<string>;
  };
  interactivity?: {
    expandableContent?: boolean;
    clickableSteps?: boolean;
    showProgress?: boolean;
  };
};

export type ContentGridsBlock = {
  _type: "contentGridsBlock";
  title?: string;
  subtitle?: string;
  dataSource?: DataSource;
  manualItems?: Array<{
    _key: string;
  } & ContentItem>;
  gridConfig?: GridConfig;
  filterConfig?: FilterConfig;
  enableLoadMore?: boolean;
  initialItemCount?: number;
  backgroundColor?: SimplerColor;
  imageFit?: "cover" | "contain";
  anchorId?: string;
};

export type FilterConfig = {
  _type: "filterConfig";
  enableSearch?: boolean;
  searchPlaceholder?: string;
  enableCategoryFilter?: boolean;
  enableTagFilter?: boolean;
  enableStatusFilter?: boolean;
  enableFeaturedFilter?: boolean;
  defaultSort?: "date-desc" | "date-asc" | "title-asc" | "title-desc" | "priority-desc" | "featured-first";
  showItemCount?: boolean;
  enableUrlFilters?: boolean;
};

export type GridLayoutBlock = {
  _type: "gridLayoutBlock";
  title?: string;
  displayTitle?: boolean;
  publicTitle?: string;
  description?: string;
  gridConfig?: GridConfig;
  gridStyling?: SectionStyling;
  gridItems?: Array<{
    gridItemConfig?: GridItemConfig;
    block?: Array<{
      _key: string;
    } & HeroBlock | {
      _key: string;
    } & HeaderBlock | {
      _key: string;
    } & FeatureCardsBlock | {
      _key: string;
    } & FeaturesMinimalBlock | {
      _key: string;
    } & FreeformBlock | {
      _key: string;
    } & PortableTextBlock | {
      _key: string;
    } & CallToActionBlock | {
      _key: string;
    } & LogoBlock | {
      _key: string;
    } & TestimonialBlock | {
      _key: string;
    } & ServicesBlock | {
      _key: string;
    } & FormBlock | {
      _key: string;
    } & MediaBlock>;
    _type: "gridItem";
    _key: string;
  }>;
};

export type SectionStyling = {
  _type: "sectionStyling";
  spacing?: "none" | "small" | "medium" | "large" | "xlarge";
  backgroundColor?: "transparent" | "white" | "gray" | "dark" | "primary" | "secondary";
  containerStyle?: "full" | "contained";
  containerWidth?: "standard" | "wide" | "full";
  verticalPadding?: "none" | "small" | "medium" | "large" | "xlarge";
  horizontalPadding?: "none" | "small" | "medium" | "large" | "xlarge";
  borderRadius?: "none" | "small" | "medium" | "large" | "full";
  shadowSm?: "none" | "small" | "medium" | "large" | "xlarge";
};

export type GridConfig = {
  _type: "gridConfig";
  columns?: {
    mobile?: number;
    tablet?: number;
    desktop?: number;
  };
  gap?: "0" | "1" | "2" | "4" | "6" | "8" | "12";
  alignment?: "start" | "center" | "end" | "stretch";
  justifyContent?: "start" | "center" | "end" | "between" | "around" | "evenly";
};

export type FormBlock = {
  _type: "formBlock";
  heading?: string;
  content?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal";
    listItem?: never;
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
  form?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "form";
  };
  anchorId?: string;
};

export type ServicesBlock = {
  _type: "servicesBlock";
  heading?: string;
  services?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "service";
  }>;
  background?: "white" | "pattern";
  topCornerRadius?: "straight" | "rounded-sm";
  buttons?: Array<{
    _key: string;
  } & ButtonObject>;
  anchorId?: string;
  paddingTop?: "none" | "small" | "medium" | "default" | "large";
  paddingBottom?: "none" | "small" | "medium" | "default" | "large";
};

export type TestimonialBlock = {
  _type: "testimonialBlock";
  heading?: string;
  eyebrow?: string;
  testimonials?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "testimonial";
  }>;
  cornerRadiusTop?: "rounded-sm" | "straight";
  cornerRadiusBottom?: "rounded-sm" | "straight";
  anchorId?: string;
};

export type LogoBlock = {
  _type: "logoBlock";
  heading?: string;
  logos?: Array<{
    title?: string;
    image?: {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      _type: "image";
    };
    size?: "default" | "large";
    link?: string;
    _key: string;
  }>;
  anchorId?: string;
};

export type CallToActionBlock = {
  _type: "callToActionBlock";
  heading?: string;
  content?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal";
    listItem?: never;
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
  buttons?: Array<{
    _key: string;
  } & ButtonObject>;
  anchorId?: string;
};

export type PortableTextBlock = {
  _type: "portableTextBlock";
  title?: string;
  content?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "blockquote";
    listItem?: "bullet" | "number";
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  } | {
    _key: string;
  } & CallToActionObject>;
  alignment?: "left" | "center" | "right";
  anchorId?: string;
};

export type FreeformBlock = {
  _type: "freeformBlock";
  title?: string;
  columnsPerRow?: "2" | "3" | "4";
  columns?: Array<{
    title?: string;
    spacing?: "none" | "small" | "medium" | "large";
    alignment?: "left" | "center" | "right";
    items?: Array<{
      _key: string;
    } & SpacerObject | {
      _key: string;
    } & HeadingObject | {
      _key: string;
    } & RichTextObject | {
      _key: string;
    } & ButtonObject | {
      _key: string;
    } & SingleImageObject>;
    _key: string;
  }>;
  border?: "none" | "topBottom" | "top" | "bottom";
  anchorId?: string;
};

export type FeaturesMinimalBlock = {
  _type: "featuresMinimalBlock";
  heading?: string;
  content?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal";
    listItem?: never;
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
  buttons?: Array<{
    _key: string;
  } & ButtonObject>;
  features?: Array<string>;
  enableBorderTop?: boolean;
  cornerRadiusTop?: "rounded-sm" | "straight";
  enableBorderBottom?: boolean;
  cornerRadiusBottom?: "rounded-sm" | "straight";
  anchorId?: string;
};

export type FeatureCardsBlock = {
  _type: "featureCardsBlock";
  heading?: string;
  buttons?: Array<{
    _key: string;
  } & ButtonObject>;
  features?: Array<{
    title?: string;
    description?: string;
    items?: Array<string>;
    image?: {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      _type: "image";
    };
    button?: {
      showButton?: boolean;
      buttonText?: string;
      buttonType?: "internal" | "anchor" | "external" | "fileDownload" | "emailAddress";
      buttonAnchorLocation?: "currentPage" | "choosePage";
      buttonPageReference?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "page";
      } | {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "blogPage";
      } | {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "servicesPage";
      } | {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "projectsPage";
      } | {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "service";
      } | {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "project";
      } | {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "center";
      } | {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "fleet";
      } | {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "brand";
      } | {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "b2bSolutionsPage";
      } | {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "fleetPage";
      } | {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "brandsPage";
      } | {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "searchPage";
      } | {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "offersPage";
      } | {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "offer";
      } | {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "b2bSolution";
      } | {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "vehicleModel";
      };
      buttonAnchorId?: string;
      buttonExternalUrl?: string;
      buttonEmailAddress?: string;
      buttonFileUrl?: {
        asset?: {
          _ref: string;
          _type: "reference";
          _weak?: boolean;
          [internalGroqTypeReferenceTo]?: "sanity.fileAsset";
        };
        media?: unknown;
        _type: "file";
      };
      buttonVariant?: "primary" | "secondary" | "tertiary" | "outline-solid" | "underline";
      buttonWidth?: "auto" | "fullWidth";
    };
    _key: string;
  }>;
  showCallToAction?: boolean;
  callToActionHeading?: string;
  callToActionContent?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal";
    listItem?: never;
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
  callToActionButtons?: Array<{
    _key: string;
  } & ButtonObject>;
  anchorId?: string;
};

export type HeaderBlock = {
  _type: "headerBlock";
  heading?: string;
  content?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal";
    listItem?: never;
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
  bottomCornerRadius?: "straight" | "rounded-sm";
  anchorId?: string;
};

export type HeroBlock = {
  _type: "heroBlock";
  heading?: string;
  content?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal";
    listItem?: never;
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
  buttons?: Array<{
    _key: string;
  } & ButtonObject>;
  blockType?: "banner" | "media" | "carousel";
  media?: MediaBlock;
  image?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    altText?: string;
    height?: "full" | "short";
    _type: "image";
  };
  layout?: "vertical" | "horizontal" | "background";
  blockOrder?: "first" | "second";
  dialogType?: "none" | "video";
  videoUrl?: string;
  overlayType?: "none" | "dark";
  anchorId?: string;
};

export type MediaBlock = {
  _type: "mediaBlock";
  backgroundType?: "image" | "muxVideo";
  backgroundWidth?: "full" | "contained";
  image?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    altText?: string;
    _type: "image";
  };
  overlayType?: "none" | "dark";
  dialogType?: "none" | "video";
  videoUrl?: string;
  muxVideo?: MuxVideoObject;
  anchorId?: string;
};

export type PageBuilder = Array<{
  _key: string;
} & HeroBlock | {
  _key: string;
} & HeaderBlock | {
  _key: string;
} & FeatureCardsBlock | {
  _key: string;
} & FeaturesMinimalBlock | {
  _key: string;
} & FreeformBlock | {
  _key: string;
} & PortableTextBlock | {
  _key: string;
} & CallToActionBlock | {
  _key: string;
} & LogoBlock | {
  _key: string;
} & TestimonialBlock | {
  _key: string;
} & ServicesBlock | {
  _key: string;
} & FormBlock | {
  _key: string;
} & MediaBlock | {
  _key: string;
} & GridLayoutBlock | {
  _key: string;
} & ContentGridsBlock | {
  _key: string;
} & ProcessTimelinesBlock | {
  _key: string;
} & StatisticsBlock | {
  _key: string;
} & CarouselBlock | {
  _key: string;
} & VehicleListBlock>;

export type Form = {
  _id: string;
  _type: "form";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  submitButtonText?: string;
  fields?: Array<{
    name?: string;
    placeholder?: string;
    inputType?: "text" | "textarea" | "email" | "tel";
    isRequired?: boolean;
    _key: string;
  }>;
};

export type Testimonial = {
  _id: string;
  _type: "testimonial";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  name?: string;
  jobTitle?: string;
  company?: string;
  quote?: string;
  avatar?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  logo?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  orderRank?: string;
};

export type Post = {
  _id: string;
  _type: "post";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  slug?: Slug;
  category?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "postCategory";
  };
  author?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "author";
  };
  excerpt?: string;
  content?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "blockquote";
    listItem?: "bullet" | "number";
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  } | {
    _key: string;
  } & CallToActionObject | {
    _key: string;
  } & SingleImageObject | {
    _key: string;
  } & VideoObject>;
  image?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    altText?: string;
    caption?: string;
    _type: "image";
  };
  relatedPostsType?: "autofill" | "custom";
  customRelatedPosts?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "post";
  }>;
  seo?: SeoObject;
  contentScope?: "consumer" | "b2b";
  b2b?: B2bPost;
  orderRank?: string;
};

export type B2bPost = {
  _type: "b2bPost";
  contentType?: "case_study" | "white_paper" | "industry_insight" | "fleet_news" | "business_guide" | "thought_leadership" | "market_analysis" | "best_practices";
  targetAudience?: Array<"fleet_managers" | "executives" | "procurement" | "finance_directors" | "operations_managers" | "sustainability_officers" | "business_owners" | "consultants">;
  businessValue?: string;
  industryRelevance?: Array<"automotive" | "construction" | "logistics" | "healthcare" | "finance" | "technology" | "manufacturing" | "retail" | "consulting" | "real_estate" | "hospitality" | "agriculture" | "energy" | "education" | "media" | "government" | "non_profit" | "cross_industry">;
  expertiseLevel?: "beginner" | "intermediate" | "expert";
  downloadable?: boolean;
  fileType?: "pdf" | "document" | "spreadsheet" | "presentation";
  file?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.fileAsset";
    };
    media?: unknown;
    _type: "file";
  };
  gatedContent?: boolean;
};

export type Author = {
  _id: string;
  _type: "author";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  name?: string;
  username?: string;
  bio?: string;
  avatar?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
};

export type PostCategory = {
  _id: string;
  _type: "postCategory";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  slug?: Slug;
  categoryColor?: SimplerColor;
  categoryScope?: "consumer" | "b2b";
  b2b?: B2bPostCategory;
  orderRank?: string;
};

export type B2bPostCategory = {
  _type: "b2bPostCategory";
  businessFocus?: "fleet_management" | "business_solutions" | "cost_optimization" | "sustainability" | "technology" | "industry_insights" | "thought_leadership" | "case_studies" | "regulatory" | "market_trends";
  contentTypes?: Array<"case_studies" | "white_papers" | "industry_insights" | "fleet_news" | "business_guides" | "thought_leadership" | "market_analysis" | "best_practices" | "trends_reports" | "technology_reviews">;
  expertiseAreas?: Array<"fleet_operations" | "vehicle_procurement" | "cost_management" | "sustainability" | "electromobility" | "technology_integration" | "risk_management" | "compliance" | "financial_planning" | "strategic_planning">;
  targetAudience?: "fleet_managers" | "c_level" | "procurement" | "finance" | "operations" | "sustainability" | "business_owners";
  contentGoals?: Array<"education" | "lead_generation" | "thought_leadership" | "customer_retention" | "brand_awareness" | "product_promotion">;
  publicationFrequency?: "weekly" | "bi_weekly" | "monthly" | "quarterly" | "as_needed";
};

export type Redirect = {
  _id: string;
  _type: "redirect";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  source?: string;
  destination?: string;
  permanent?: boolean;
  isEnabled?: boolean;
};

export type BlogSettings = {
  _id: string;
  _type: "blogSettings";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  showRelatedPosts?: boolean;
  showTableOfContents?: boolean;
  showPostsByCategory?: boolean;
};

export type MarketingSettings = {
  _id: string;
  _type: "marketingSettings";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  googleAnalyticsId?: string;
  googleTagManagerId?: string;
};

export type NavigationSettings = {
  _id: string;
  _type: "navigationSettings";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  navbarMenuItems?: Array<{
    menuItemType?: "single" | "group";
    title?: string;
    pageReference?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "page";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "blogPage";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "servicesPage";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "projectsPage";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "service";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "project";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "center";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "fleet";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "brand";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "b2bSolutionsPage";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "fleetPage";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "brandsPage";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "searchPage";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "offersPage";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "offer";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "b2bSolution";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "vehicleModel";
    };
    showChildren?: boolean;
    pageReferences?: Array<{
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "page";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "blogPage";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "servicesPage";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "projectsPage";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "service";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "project";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "center";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "fleet";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "brand";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "b2bSolutionsPage";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "fleetPage";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "brandsPage";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "searchPage";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "offersPage";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "offer";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "b2bSolution";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "vehicleModel";
    }>;
    isButton?: boolean;
    _key: string;
  }>;
  showSlideOutMenu?: boolean;
  slideOutMenuItems?: Array<{
    menuItemType?: "single" | "group";
    title?: string;
    pageReference?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "page";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "blogPage";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "servicesPage";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "projectsPage";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "service";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "project";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "center";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "fleet";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "brand";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "b2bSolutionsPage";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "fleetPage";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "brandsPage";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "searchPage";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "offersPage";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "offer";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "b2bSolution";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "vehicleModel";
    };
    showChildren?: boolean;
    pageReferences?: Array<{
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "page";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "blogPage";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "servicesPage";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "projectsPage";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "service";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "project";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "center";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "fleet";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "brand";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "b2bSolutionsPage";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "fleetPage";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "brandsPage";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "searchPage";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "offersPage";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "offer";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "b2bSolution";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "vehicleModel";
    }>;
    isButton?: boolean;
    _key: string;
  }>;
  slideOutMenuButtons?: Array<{
    _key: string;
  } & ButtonObject>;
  showCompanyDetailsSlideOutMenu?: boolean;
  footerColumns?: Array<{
    title?: string;
    menuItems?: Array<{
      title?: string;
      linkType?: "internal" | "external";
      pageReference?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "page";
      } | {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "blogPage";
      } | {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "servicesPage";
      } | {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "projectsPage";
      } | {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "service";
      } | {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "project";
      } | {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "center";
      } | {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "fleet";
      } | {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "brand";
      } | {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "b2bSolutionsPage";
      } | {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "fleetPage";
      } | {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "brandsPage";
      } | {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "searchPage";
      } | {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "offersPage";
      } | {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "offer";
      } | {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "b2bSolution";
      } | {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "vehicleModel";
      };
      externalUrl?: string;
      _key: string;
    }>;
    _key: string;
  }>;
  footerLegalMenuItems?: Array<{
    title?: string;
    pageReference?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "page";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "blogPage";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "servicesPage";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "projectsPage";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "service";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "project";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "center";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "fleet";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "brand";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "b2bSolutionsPage";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "fleetPage";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "brandsPage";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "searchPage";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "offersPage";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "offer";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "b2bSolution";
    } | {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "vehicleModel";
    };
    _key: string;
  }>;
};

export type GeneralSettings = {
  _id: string;
  _type: "generalSettings";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  siteTitle?: string;
  siteLogo?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  homePage?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "page";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "blogPage";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "servicesPage";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "projectsPage";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "service";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "project";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "center";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "fleet";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "brand";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "b2bSolutionsPage";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "fleetPage";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "brandsPage";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "searchPage";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "offersPage";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "offer";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "b2bSolution";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "vehicleModel";
  };
  companyEmailAddress?: string;
  companyPhoneNumber?: string;
  companySocialMediaLinks?: Array<{
    title?: string;
    profileUrl?: string;
    _key: string;
  }>;
};

export type Page = {
  _id: string;
  _type: "page";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  slug?: Slug;
  parent?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "page";
  };
  children?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "page";
  }>;
  pageBuilder?: PageBuilder;
  seo?: SeoObject;
};

export type BlogPage = {
  _id: string;
  _type: "blogPage";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  slug?: Slug;
  pageBuilder?: PageBuilder;
  seo?: SeoObject;
};

export type ServicesPage = {
  _id: string;
  _type: "servicesPage";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  slug?: Slug;
  pageBuilder?: PageBuilder;
  seo?: SeoObject;
};

export type ProjectsPage = {
  _id: string;
  _type: "projectsPage";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  slug?: Slug;
  pageBuilder?: PageBuilder;
  seo?: SeoObject;
};

export type Service = {
  _id: string;
  _type: "service";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  slug?: Slug;
  shortDescription?: string;
  image?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    altText?: string;
    caption?: string;
    _type: "image";
  };
  serviceScope?: "consumer" | "b2b";
  b2b?: B2bService;
  pageBuilder?: PageBuilder;
  seo?: SeoObject;
  orderRank?: string;
};

export type Project = {
  _id: string;
  _type: "project";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  slug?: Slug;
  category?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "projectCategory";
  };
  excerpt?: string;
  image?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    altText?: string;
    caption?: string;
    _type: "image";
  };
  projectType?: "consumer" | "b2b";
  b2b?: B2bProject;
  pageBuilder?: PageBuilder;
  seo?: SeoObject;
};

export type Center = {
  _id: string;
  _type: "center";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  name?: string;
  slug?: Slug;
  centerType?: "audi" | "skoda" | "occasions" | "multibrands";
  autoscoutSellerId?: string;
  shortDescription?: string;
  image?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    altText?: string;
    caption?: string;
    _type: "image";
  };
  pageBuilder?: PageBuilder;
  address?: AddressObject;
  contact?: ContactObject;
  openingHours?: Array<{
    day?: "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" | "Saturday" | "Sunday";
    hours?: string;
    _key: string;
  }>;
  brandConfig?: {
    primaryColor?: SimplerColor;
    secondaryColor?: SimplerColor;
    logo?: {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      altText?: string;
      _type: "image";
    };
  };
  certifications?: Array<{
    name?: string;
    logo?: {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      _type: "image";
    };
    description?: string;
    _key: string;
  }>;
  specialties?: Array<"New Vehicles" | "Used Vehicles" | "Electric Vehicles" | "Sport Vehicles" | "Luxury Vehicles" | "Family Vehicles" | "Professional Leasing" | "After-Sales Service" | "Body Shop" | "Parts & Accessories" | "Vehicle Trade-In" | "Custom Financing">;
  team?: Array<{
    name?: string;
    role?: "Center Director" | "Sales Advisor" | "Service Advisor" | "Workshop Manager" | "Finance Advisor" | "Service Reception";
    photo?: {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      _type: "image";
    };
    directPhone?: string;
    directEmail?: string;
    specialization?: Array<string>;
    _key: string;
  }>;
  serviceAreas?: Array<string>;
  apiSettings?: {
    enableAutoSync?: boolean;
    syncFrequency?: "15min" | "1hour" | "6hours" | "daily";
  };
  searchDefaults?: {
    defaultSort?: "price_asc" | "price_desc" | "year_desc" | "mileage_asc" | "relevance";
    itemsPerPage?: number;
  };
  displaySettings?: {
    showPrices?: boolean;
    showFinancing?: boolean;
    featuredBadge?: string;
  };
  seo?: SeoObject;
};

export type ContactObject = {
  _type: "contactObject";
  phone?: string;
  email?: string;
  website?: string;
  whatsapp?: string;
};

export type AddressObject = {
  _type: "addressObject";
  street?: string;
  postalCode?: string;
  city?: string;
  region?: string;
  country?: string;
  coordinates?: Geopoint;
};

export type B2bSolutionsPage = {
  _id: string;
  _type: "b2bSolutionsPage";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  slug?: Slug;
  pageBuilder?: PageBuilder;
  seo?: SeoObject;
};

export type FleetPage = {
  _id: string;
  _type: "fleetPage";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  slug?: Slug;
  pageBuilder?: PageBuilder;
  seo?: SeoObject;
};

export type BrandsPage = {
  _id: string;
  _type: "brandsPage";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  slug?: Slug;
  pageBuilder?: PageBuilder;
  seo?: SeoObject;
};

export type OffersPage = {
  _id: string;
  _type: "offersPage";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  slug?: Slug;
  pageBuilder?: PageBuilder;
  seo?: SeoObject;
};

export type AnimationConfig = {
  _type: "animationConfig";
  animationType?: "countUp" | "spring" | "bounce" | "typewriter" | "fadeIn" | "scaleUp";
  duration?: number;
  delay?: number;
  trigger?: "inView" | "load" | "hover" | "manual";
  easing?: "ease" | "easeIn" | "easeOut" | "easeInOut" | "linear";
  springConfig?: {
    mass?: number;
    stiffness?: number;
    damping?: number;
  };
  viewportConfig?: {
    threshold?: number;
    once?: boolean;
  };
};

export type DataSource = {
  _type: "dataSource";
  sourceType?: "static" | "documents" | "api" | "groq";
  outputFormat?: "number" | "documents" | "text" | "raw";
  staticConfig?: {
    value?: string;
  };
  documentConfig?: {
    documentType?: "post" | "project" | "service" | "testimonial" | "author" | "page" | "form" | "postCategory" | "projectCategory" | "center" | "brand" | "vehicleModel" | "offer" | "b2bSolution" | "fleet" | "custom";
    customDocumentType?: string;
    filters?: Array<{
      field?: string;
      operator?: "==" | "!=" | ">" | "<" | ">=" | "<=" | "match" | "in" | "references";
      value?: string;
      _key: string;
    }>;
    sortBy?: {
      field?: string;
      order?: "asc" | "desc";
    };
    limit?: number;
    projection?: string;
  };
  apiConfig?: {
    endpoint?: string;
    method?: "GET" | "POST";
    headers?: Array<{
      key?: string;
      value?: string;
      _key: string;
    }>;
    requestBody?: string;
    responseTransform?: {
      dataPath?: string;
      transform?: string;
    };
    cacheConfig?: {
      enabled?: boolean;
      duration?: number;
    };
  };
  groqConfig?: {
    query?: string;
    params?: Array<{
      key?: string;
      value?: string;
      type?: "string" | "number" | "boolean" | "reference";
      _key: string;
    }>;
  };
  errorHandling?: {
    fallbackValue?: string;
    retryAttempts?: number;
    timeout?: number;
  };
};

export type GridItemConfig = {
  _type: "gridItemConfig";
  columnSpan?: {
    mobile?: number;
    tablet?: number;
    desktop?: number;
  };
  rowSpan?: number;
  startColumn?: {
    mobile?: number;
    tablet?: number;
    desktop?: number;
  };
  startRow?: {
    mobile?: number;
    tablet?: number;
    desktop?: number;
  };
  order?: {
    mobile?: number;
    tablet?: number;
    desktop?: number;
  };
  alignment?: "inherit" | "start" | "center" | "end" | "stretch";
  customStyling?: {
    backgroundColor?: SimplerColor;
    textColor?: SimplerColor;
    padding?: "none" | "small" | "medium" | "large" | "xlarge";
    borderRadius?: "none" | "sm" | "md" | "lg" | "full";
    shadowSm?: "none" | "sm" | "md" | "lg" | "xl";
  };
};

export type Offer = {
  _id: string;
  _type: "offer";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  slug?: Slug;
  brand?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "brand";
  };
  offerCode?: string;
  offerType?: "financing" | "leasing" | "discount" | "rebate" | "trade_in" | "loyalty" | "referral" | "seasonal" | "promotional" | "clearance" | "bundle" | "enterprise";
  shortDescription?: string;
  description?: RichTextObject;
  keyBenefits?: Array<{
    title?: string;
    description?: string;
    icon?: {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      _type: "image";
    };
    _key: string;
  }>;
  mainImage?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    altText?: string;
    caption?: string;
    _type: "image";
  };
  gallery?: Array<{
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    altText?: string;
    caption?: string;
    _type: "image";
    _key: string;
  }>;
  offerVideo?: MuxVideoObject;
  validityPeriod?: {
    validFrom?: string;
    validTo?: string;
    isLimitedTime?: boolean;
  };
  maxRedemptions?: number;
  currentRedemptions?: number;
  targetAudience?: Array<"private" | "business" | "fleet" | "students" | "seniors" | "military" | "employees" | "existing" | "new">;
  eligibilityCriteria?: Array<{
    title?: string;
    description?: string;
    isRequired?: boolean;
    _key: string;
  }>;
  applicableModels?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "vehicleModel";
  }>;
  excludedModels?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "vehicleModel";
  }>;
  applicableCategories?: Array<"sedan" | "hatchback" | "suv" | "coupe" | "convertible" | "wagon" | "crossover" | "pickup" | "van" | "roadster">;
  financingDetails?: {
    financingType?: "loan" | "lease" | "balloon" | "hire_purchase" | "pcp" | "cash";
    interestRate?: number;
    duration?: number;
    downPayment?: number;
    monthlyPayment?: number;
    finalPayment?: number;
    totalAmount?: number;
    currency?: "CHF" | "EUR" | "USD";
  };
  savings?: {
    discountType?: "percentage" | "fixed" | "tiered" | "bogo";
    discountValue?: number;
    discountPercentage?: number;
    maximumDiscount?: number;
    rebateAmount?: number;
    tradeInBonus?: number;
    loyaltyBonus?: number;
    referralBonus?: number;
  };
  promotionalPricing?: {
    originalPrice?: number;
    promotionalPrice?: number;
    pricingNotes?: string;
  };
  contactInformation?: {
    contactPerson?: string;
    contactRole?: string;
    contactPhone?: string;
    contactEmail?: string;
    contactCenter?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "center";
    };
  };
  onlineApplication?: {
    isEnabled?: boolean;
    applicationUrl?: string;
    applicationFormId?: string;
  };
  legalDisclaimer?: RichTextObject;
  termsAndConditions?: RichTextObject;
  marketingMaterials?: {
    brochure?: {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.fileAsset";
      };
      media?: unknown;
      _type: "file";
    };
    socialMediaAssets?: Array<{
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      platform?: "facebook" | "instagram" | "linkedin" | "twitter" | "general";
      altText?: string;
      _type: "image";
      _key: string;
    }>;
    emailTemplate?: RichTextObject;
    webBanner?: {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      _type: "image";
    };
  };
  trackingSettings?: {
    trackingCode?: string;
    utmSource?: string;
    utmMedium?: string;
    utmCampaign?: string;
  };
  relatedOffers?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "offer";
  }>;
  displaySettings?: {
    isActive?: boolean;
    isFeatured?: boolean;
    showOnHomepage?: boolean;
    showInNavigation?: boolean;
    priority?: number;
  };
  approvalStatus?: "draft" | "pending" | "approved" | "rejected" | "revision";
  approvedBy?: string;
  approvalDate?: string;
  reviewNotes?: string;
  internalNotes?: string;
  seo?: SeoObject;
};

export type SearchPage = {
  _id: string;
  _type: "searchPage";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  slug?: Slug;
  desktopFilterConfig?: {
    title?: string;
    subtitle?: string;
    showHeader?: boolean;
    showSelectedFilters?: boolean;
    multiSelect?: boolean;
    filters?: Array<{
      _key: string;
    } & FilterItem>;
  };
  mobileFilterConfig?: {
    title?: string;
    subtitle?: string;
    showHeader?: boolean;
    showSelectedFilters?: boolean;
    multiSelect?: boolean;
    filters?: Array<{
      _key: string;
    } & FilterItem>;
  };
  seo?: SeoObject;
};

export type Fleet = {
  _id: string;
  _type: "fleet";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  name?: string;
  slug?: Slug;
  linkedSolution?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "b2bSolution";
  };
  brand?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "brand";
  };
  shortDescription?: string;
  description?: string;
  heroImage?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    altText?: string;
    _type: "image";
  };
  fleetType?: "corporate" | "sme" | "startup" | "government" | "rental" | "delivery" | "service" | "sales" | "executive";
  fleetSize?: {
    minVehicles?: number;
    maxVehicles?: number;
    recommendedSize?: number;
  };
  usagePattern?: "daily" | "weekly" | "occasional" | "intensive" | "mixed";
  availableModels?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "vehicleModel";
  }>;
  vehicleConfigurations?: Array<{
    model?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "vehicleModel";
    };
    configurationName?: string;
    fleetDiscount?: number;
    minimumQuantity?: number;
    image?: {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      _type: "image";
    };
    _key: string;
  }>;
  includedServices?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "service";
  }>;
  additionalServices?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "service";
  }>;
  serviceLevel?: "basic" | "standard" | "premium" | "enterprise";
  customerDeliveries?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "project";
  }>;
  deliveryHighlights?: Array<{
    title?: string;
    description?: string;
    image?: {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      _type: "image";
    };
    _key: string;
  }>;
  managementFeatures?: Array<{
    feature?: "business_benefit_portal" | "fleet_optimization" | "electromobility" | "maintenance" | "reporting" | "tracking" | "insurance" | "fuel";
    title?: string;
    description?: string;
    isActive?: boolean;
    _key: string;
  }>;
  fleetBenefits?: Array<{
    title?: string;
    description?: string;
    icon?: {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      _type: "image";
    };
    _key: string;
  }>;
  displaySettings?: {
    isActive?: boolean;
    isPremium?: boolean;
    requiresAssessment?: boolean;
    customConfigRequired?: boolean;
  };
  seo?: SeoObject;
};

export type VehicleModel = {
  _id: string;
  _type: "vehicleModel";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  name?: string;
  slug?: Slug;
  brand?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "brand";
  };
  modelCode?: string;
  modelYear?: number;
  generation?: string;
  category?: "sedan" | "hatchback" | "suv" | "coupe" | "convertible" | "wagon" | "crossover" | "pickup" | "van" | "roadster";
  bodyStyle?: Array<"2door" | "4door" | "5door" | "cabriolet" | "targa" | "shooting_brake">;
  marketSegment?: Array<"compact" | "mid_size" | "full_size" | "luxury" | "performance" | "electric">;
  shortDescription?: string;
  description?: RichTextObject;
  keyFeatures?: Array<{
    title?: string;
    description?: string;
    icon?: {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      _type: "image";
    };
    _key: string;
  }>;
  designPhilosophy?: RichTextObject;
  mainImage?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    altText?: string;
    caption?: string;
    _type: "image";
  };
  gallery?: Array<{
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    altText?: string;
    caption?: string;
    _type: "image";
    _key: string;
  }>;
  modelVideo?: MuxVideoObject;
  configuratorImages?: Array<{
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    altText?: string;
    angle?: "front" | "rear" | "side" | "interior" | "three_quarter_front" | "three_quarter_rear";
    _type: "image";
    _key: string;
  }>;
  engineSpecs?: Array<{
    engineName?: string;
    engineType?: "gasoline" | "diesel" | "electric" | "hybrid" | "plugin_hybrid" | "hydrogen";
    displacement?: number;
    power?: number;
    torque?: number;
    transmission?: "manual" | "automatic" | "cvt" | "dual_clutch" | "single_speed";
    drivetrain?: "fwd" | "rwd" | "awd" | "4wd";
    _key: string;
  }>;
  fuelConsumption?: {
    cityConsumption?: number;
    highwayConsumption?: number;
    combinedConsumption?: number;
    co2Emissions?: number;
  };
  performanceSpecs?: {
    acceleration?: number;
    topSpeed?: number;
  };
  dimensions?: {
    length?: number;
    width?: number;
    height?: number;
    wheelbase?: number;
    groundClearance?: number;
    curbWeight?: number;
    grossWeight?: number;
    trunkCapacity?: number;
    fuelTankCapacity?: number;
    seatingCapacity?: number;
    doors?: number;
  };
  safety?: Array<string>;
  technology?: Array<string>;
  comfort?: Array<string>;
  standardEquipment?: Array<{
    name?: string;
    description?: string;
    category?: "safety" | "technology" | "comfort" | "performance" | "exterior" | "interior";
    _key: string;
  }>;
  optionalEquipment?: Array<{
    name?: string;
    description?: string;
    price?: number;
    category?: "safety" | "technology" | "comfort" | "performance" | "exterior" | "interior";
    _key: string;
  }>;
  exteriorColors?: Array<{
    name?: string;
    code?: string;
    image?: {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      _type: "image";
    };
    price?: number;
    _key: string;
  }>;
  interiorOptions?: Array<{
    name?: string;
    type?: "leather" | "fabric" | "synthetic" | "alcantara" | "wood" | "carbon_fiber" | "metal";
    image?: {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      _type: "image";
    };
    price?: number;
    _key: string;
  }>;
  availability?: {
    productionStatus?: "current" | "upcoming" | "discontinued" | "limited";
    deliveryTime?: string;
    marketLaunch?: string;
    discontinuedDate?: string;
  };
  warrantyInfo?: {
    vehicleWarranty?: number;
    mileageWarranty?: number;
    batteryWarranty?: number;
    paintWarranty?: number;
  };
  awards?: Array<{
    title?: string;
    year?: number;
    awardingBody?: string;
    description?: string;
    _key: string;
  }>;
  relatedModels?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "vehicleModel";
  }>;
  competitorModels?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "vehicleModel";
  }>;
  pricing?: {
    basePrice?: number;
    currency?: "CHF" | "EUR" | "USD";
    priceValidFrom?: string;
    priceValidTo?: string;
    includedTaxes?: Array<string>;
    excludedCosts?: Array<string>;
  };
  displaySettings?: {
    isActive?: boolean;
    isFeatured?: boolean;
    showInConfigurator?: boolean;
    configuratorOrder?: number;
    showPricing?: boolean;
    allowConfiguration?: boolean;
  };
  seo?: SeoObject;
};

export type B2bSolution = {
  _id: string;
  _type: "b2bSolution";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  name?: string;
  slug?: Slug;
  brand?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "brand";
  };
  solutionType?: "comprehensive" | "fleet" | "leasing" | "service" | "consultation";
  shortDescription?: string;
  targetSegments?: Array<"sme" | "enterprise" | "fleet" | "professionals" | "startups" | "government" | "non_profit" | "education" | "healthcare" | "financial">;
  businessSizes?: Array<"micro" | "small" | "medium" | "large" | "enterprise">;
  industries?: Array<"automotive" | "construction" | "consulting" | "education" | "finance" | "healthcare" | "hospitality" | "manufacturing" | "real_estate" | "retail" | "technology" | "government" | "non_profit" | "agriculture" | "energy" | "logistics" | "telecommunications" | "media" | "legal" | "insurance" | "food_beverage">;
  geographicScope?: "local" | "regional" | "national" | "international";
  serviceCategories?: Array<{
    title?: string;
    description?: string;
    icon?: {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      _type: "image";
    };
    services?: Array<{
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      _key: string;
      [internalGroqTypeReferenceTo]?: "service";
    }>;
    _key: string;
  }>;
  featuredServices?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "service";
  }>;
  fleetSolutions?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "fleet";
  }>;
  fleetIntegrationLevel?: "basic" | "standard" | "premium" | "enterprise";
  supportedFleetTypes?: Array<"corporate" | "sme" | "startup" | "government" | "rental" | "delivery" | "service" | "sales" | "executive">;
  businessBenefits?: Array<{
    title?: string;
    metric?: string;
    description?: string;
    icon?: {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      _type: "image";
    };
    _key: string;
  }>;
  competitiveAdvantages?: Array<{
    title?: string;
    description?: string;
    _key: string;
  }>;
  keyContacts?: Array<{
    name?: string;
    role?: string;
    email?: string;
    phone?: string;
    photo?: {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      altText?: string;
      _type: "image";
    };
    center?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "center";
    };
    specialties?: Array<string>;
    bio?: string;
    _key: string;
  }>;
  featuredProjects?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "project";
  }>;
  successStories?: Array<{
    title?: string;
    description?: string;
    image?: {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      _type: "image";
    };
    _key: string;
  }>;
  featuredContent?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "post";
  }>;
  displaySettings?: {
    isActive?: boolean;
    isPremium?: boolean;
    requiresConsultation?: boolean;
    customQuoteRequired?: boolean;
  };
  seo?: SeoObject;
};

export type Brand = {
  _id: string;
  _type: "brand";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  name?: string;
  slug?: Slug;
  brandType?: Array<"premium" | "luxury" | "electric" | "sports" | "family" | "business" | "economy" | "performance">;
  shortDescription?: string;
  tagline?: string;
  description?: string;
  brandStory?: RichTextObject;
  brandValues?: Array<{
    title?: string;
    description?: string;
    icon?: {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      _type: "image";
    };
    _key: string;
  }>;
  logo?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    altText?: string;
    _type: "image";
  };
  logoVariants?: Array<{
    variant?: "primary" | "white" | "dark" | "symbol" | "horizontal" | "vertical";
    logo?: {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      altText?: string;
      _type: "image";
    };
    _key: string;
  }>;
  brandColors?: {
    primaryColor?: SimplerColor;
    secondaryColor?: SimplerColor;
    accentColor?: SimplerColor;
  };
  brandImages?: Array<{
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    altText?: string;
    caption?: string;
    _type: "image";
    _key: string;
  }>;
  brandVideo?: MuxVideoObject;
  keyMessage?: string;
  targetAudience?: Array<"individuals" | "families" | "professionals" | "corporates" | "enthusiasts" | "luxury">;
  countryOfOrigin?: string;
  foundedYear?: number;
  headquarters?: string;
  websiteUrl?: string;
  warranty?: RichTextObject;
  serviceNetwork?: RichTextObject;
  socialMedia?: {
    facebook?: string;
    instagram?: string;
    youtube?: string;
    twitter?: string;
    linkedin?: string;
  };
  displaySettings?: {
    isActive?: boolean;
    showInNavigation?: boolean;
    navigationOrder?: number;
    featuredBrand?: boolean;
  };
  seo?: SeoObject;
};

export type SeoObject = {
  _type: "seoObject";
  title?: string;
  description?: string;
  noIndex?: boolean;
  image?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
};

export type MuxVideoObject = {
  _type: "muxVideoObject";
  video?: MuxVideo;
  title?: string;
  description?: string;
  poster?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  aspectRatio?: "16:9" | "4:3" | "1:1" | "9:16" | "auto";
  maxResolution?: "720p" | "1080p" | "1440p" | "2160p";
  controls?: boolean;
  autoplay?: boolean;
  loop?: boolean;
  muted?: boolean;
  captions?: boolean;
  language?: "en" | "fr" | "es" | "de" | "it" | "pt";
};

export type RichTextObject = {
  _type: "richTextObject";
  richTextContent?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal";
    listItem?: never;
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
};

export type B2bProject = {
  _type: "b2bProject";
  customerName?: string;
  customerIndustry?: "automotive" | "construction" | "logistics" | "healthcare" | "finance" | "technology" | "manufacturing" | "retail" | "consulting" | "real_estate" | "hospitality" | "agriculture" | "energy" | "education" | "media" | "other";
  customerLogo?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    altText?: string;
    _type: "image";
  };
  deliveryDate?: string;
  vehicleCount?: number;
  deliveredModels?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "vehicleModel";
  }>;
  fleetType?: "corporate" | "sme" | "startup" | "government" | "rental" | "delivery" | "sales" | "service";
  projectValue?: number;
  implementationTime?: string;
  caseStudyDetails?: string;
  successMetrics?: Array<{
    metric?: string;
    value?: string;
    description?: string;
    _key: string;
  }>;
  customerTestimonial?: {
    quote?: string;
    author?: string;
    role?: string;
  };
};

export type ProjectCategory = {
  _id: string;
  _type: "projectCategory";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  slug?: Slug;
  categoryScope?: "consumer" | "b2b";
  b2b?: B2bProjectCategory;
  orderRank?: string;
};

export type B2bProjectCategory = {
  _type: "b2bProjectCategory";
  businessFocus?: "fleet_delivery" | "service_implementation" | "fleet_optimization" | "electromobility_transition" | "cost_reduction" | "sustainability" | "technology_integration" | "process_improvement";
  industryTargets?: Array<"automotive" | "construction" | "logistics" | "healthcare" | "finance" | "technology" | "manufacturing" | "retail" | "consulting" | "real_estate" | "hospitality" | "agriculture" | "energy" | "education" | "media" | "government" | "non_profit">;
  serviceTypes?: Array<"vehicle_procurement" | "fleet_management" | "financing_solutions" | "maintenance_services" | "consulting" | "training" | "technology_implementation" | "compliance_management">;
  projectComplexity?: "simple" | "moderate" | "complex" | "enterprise";
  averageProjectDuration?: number;
  typicalVehicleCount?: number;
};

export type B2bService = {
  _type: "b2bService";
  serviceCategory?: "sales" | "financing" | "maintenance" | "digital" | "consultation";
  targetSegments?: Array<"sme" | "enterprise" | "fleet" | "professionals" | "startups">;
  businessBenefits?: string;
  deliveryMethod?: "on_site" | "remote" | "center" | "hybrid";
  responseTime?: string;
  serviceLevel?: string;
  pricingModel?: "included" | "additional" | "contact_required";
  implementationTime?: string;
  integrations?: string;
};

export type MuxVideo = {
  _type: "mux.video";
  asset?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "mux.videoAsset";
  };
};

export type MuxVideoAsset = {
  _id: string;
  _type: "mux.videoAsset";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  status?: string;
  assetId?: string;
  playbackId?: string;
  filename?: string;
  thumbTime?: number;
  data?: MuxAssetData;
};

export type MuxAssetData = {
  _type: "mux.assetData";
  resolution_tier?: string;
  upload_id?: string;
  created_at?: string;
  id?: string;
  status?: string;
  max_stored_resolution?: string;
  passthrough?: string;
  encoding_tier?: string;
  master_access?: string;
  aspect_ratio?: string;
  duration?: number;
  max_stored_frame_rate?: number;
  mp4_support?: string;
  max_resolution_tier?: string;
  tracks?: Array<{
    _key: string;
  } & MuxTrack>;
  playback_ids?: Array<{
    _key: string;
  } & MuxPlaybackId>;
  static_renditions?: MuxStaticRenditions;
};

export type MuxStaticRenditions = {
  _type: "mux.staticRenditions";
  status?: string;
  files?: Array<{
    _key: string;
  } & MuxStaticRenditionFile>;
};

export type MuxStaticRenditionFile = {
  _type: "mux.staticRenditionFile";
  ext?: string;
  name?: string;
  width?: number;
  bitrate?: number;
  filesize?: number;
  height?: number;
};

export type MuxPlaybackId = {
  _type: "mux.playbackId";
  id?: string;
  policy?: string;
};

export type MuxTrack = {
  _type: "mux.track";
  id?: string;
  type?: string;
  max_width?: number;
  max_frame_rate?: number;
  duration?: number;
  max_height?: number;
};

export type HighlightColor = {
  _type: "highlightColor";
  label?: string;
  value?: string;
};

export type TextColor = {
  _type: "textColor";
  label?: string;
  value?: string;
};

export type SimplerColor = {
  _type: "simplerColor";
  label?: string;
  value?: string;
};

export type MediaTag = {
  _id: string;
  _type: "media.tag";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  name?: Slug;
};

export type SanityImagePaletteSwatch = {
  _type: "sanity.imagePaletteSwatch";
  background?: string;
  foreground?: string;
  population?: number;
  title?: string;
};

export type SanityImagePalette = {
  _type: "sanity.imagePalette";
  darkMuted?: SanityImagePaletteSwatch;
  lightVibrant?: SanityImagePaletteSwatch;
  darkVibrant?: SanityImagePaletteSwatch;
  vibrant?: SanityImagePaletteSwatch;
  dominant?: SanityImagePaletteSwatch;
  lightMuted?: SanityImagePaletteSwatch;
  muted?: SanityImagePaletteSwatch;
};

export type SanityImageDimensions = {
  _type: "sanity.imageDimensions";
  height?: number;
  width?: number;
  aspectRatio?: number;
};

export type SanityImageHotspot = {
  _type: "sanity.imageHotspot";
  x?: number;
  y?: number;
  height?: number;
  width?: number;
};

export type SanityImageCrop = {
  _type: "sanity.imageCrop";
  top?: number;
  bottom?: number;
  left?: number;
  right?: number;
};

export type SanityFileAsset = {
  _id: string;
  _type: "sanity.fileAsset";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  originalFilename?: string;
  label?: string;
  title?: string;
  description?: string;
  altText?: string;
  sha1hash?: string;
  extension?: string;
  mimeType?: string;
  size?: number;
  assetId?: string;
  uploadId?: string;
  path?: string;
  url?: string;
  source?: SanityAssetSourceData;
};

export type SanityImageAsset = {
  _id: string;
  _type: "sanity.imageAsset";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  originalFilename?: string;
  label?: string;
  title?: string;
  description?: string;
  altText?: string;
  sha1hash?: string;
  extension?: string;
  mimeType?: string;
  size?: number;
  assetId?: string;
  uploadId?: string;
  path?: string;
  url?: string;
  metadata?: SanityImageMetadata;
  source?: SanityAssetSourceData;
};

export type SanityImageMetadata = {
  _type: "sanity.imageMetadata";
  location?: Geopoint;
  dimensions?: SanityImageDimensions;
  palette?: SanityImagePalette;
  lqip?: string;
  blurHash?: string;
  hasAlpha?: boolean;
  isOpaque?: boolean;
};

export type Geopoint = {
  _type: "geopoint";
  lat?: number;
  lng?: number;
  alt?: number;
};

export type Slug = {
  _type: "slug";
  current?: string;
  source?: string;
};

export type SanityAssetSourceData = {
  _type: "sanity.assetSourceData";
  name?: string;
  id?: string;
  url?: string;
};

export type AllSanitySchemaTypes = ConditionTypeArray | ConditionType | FuelTypeArray | FuelType | TimelineStep | FilterItem | ContentItem | VideoObject | CallToActionObject | SpacerObject | SingleImageObject | ButtonObject | HeadingObject | VehicleListBlock | CarouselBlock | CarouselConfig | StatisticsBlock | ProcessTimelinesBlock | TimelineConfig | ContentGridsBlock | FilterConfig | GridLayoutBlock | SectionStyling | GridConfig | FormBlock | ServicesBlock | TestimonialBlock | LogoBlock | CallToActionBlock | PortableTextBlock | FreeformBlock | FeaturesMinimalBlock | FeatureCardsBlock | HeaderBlock | HeroBlock | MediaBlock | PageBuilder | Form | Testimonial | Post | B2bPost | Author | PostCategory | B2bPostCategory | Redirect | BlogSettings | MarketingSettings | NavigationSettings | GeneralSettings | Page | BlogPage | ServicesPage | ProjectsPage | Service | Project | Center | ContactObject | AddressObject | B2bSolutionsPage | FleetPage | BrandsPage | OffersPage | AnimationConfig | DataSource | GridItemConfig | Offer | SearchPage | Fleet | VehicleModel | B2bSolution | Brand | SeoObject | MuxVideoObject | RichTextObject | B2bProject | ProjectCategory | B2bProjectCategory | B2bService | MuxVideo | MuxVideoAsset | MuxAssetData | MuxStaticRenditions | MuxStaticRenditionFile | MuxPlaybackId | MuxTrack | HighlightColor | TextColor | SimplerColor | MediaTag | SanityImagePaletteSwatch | SanityImagePalette | SanityImageDimensions | SanityImageHotspot | SanityImageCrop | SanityFileAsset | SanityImageAsset | SanityImageMetadata | Geopoint | Slug | SanityAssetSourceData;
export declare const internalGroqTypeReferenceTo: unique symbol;
